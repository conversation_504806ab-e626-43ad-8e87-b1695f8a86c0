Job ID,Job Link,<PERSON>sume Tried,Date listed,Date Tried,Assumed Reason,Stack Trace,External Job link,Screenshot Name
4275539160,https://www.linkedin.com/jobs/view/4275539160,Pending,2025-07-25 09:52:12.008851,2025-07-25 10:30:12.126310,Found a Bad Word in About Job,"
About the job
Role: Salesforce Marketing Cloud Developer
Location: Bangalore | Hyderabad | Pune | Gurgaon | Noida | Chennai
Experience: 2 to 8 Years
Notice Period: Immediate Joiners Only

Job Description:
PRIMARY RESPONSIBILITIES
The individual should have extensive knowledge and experience in the following areas: 

Solid hands-on experience working with Marketing Cloud (Email, Mobile, Automation Studio as well as content, contact & journey builder)
Experience on JavaScript, HTML & CSS
Expertise in Business Requirement gathering, Analysis & conceptualizing high-level framework & design 
Exposure to SFDC and its integration with SFMC
Conceptualize integration via API (inbound and outbound), sFTP and with middleware (good to have)
Experience in REST & SOAP APIs, knowledge of Salesforce limits involved during Integration
Should have experience with data modeling and solid on data transformation using SQLs
Strong experience in designing and working with large scale multi cloud applications 
Excellent consulting skills, oral and written communication, and analytical skills 
Marketing Cloud Email Specialist & Marketing Cloud Developer certifications

SECONDARY REQUIREMENTS
, Marketing Cloud Consultant
Knowledge Social Studio, Advertisement Studio & Interaction Studio
Incident and problem management skills
Ability to provide necessary coaching to bring lesser experienced up to speed on the technology 


PERSONAL ATTRIBUTES
Strong written and verbal communication skills

Contains bad word ""Sales"". Skipping this job!
",Skipped,Not Available
4275357474,https://www.linkedin.com/jobs/view/4275357474,Pending,2025-07-25 09:30:15.657208,2025-07-25 10:30:15.784265,Found a Bad Word in About Job,"
About the job
We're looking for a CCaaS Application Architect to be a pivotal part of our client's success. In this role, you will cultivate strong relationships with clients, gaining a deep understanding of their unique business and technical needs. Your expertise in Contact Center solutions and technical design will be vital in designing and engineering innovative solutions that effectively leverage our AI tools and integrate seamlessly with clients' CRM systems. 

Your Contact Center experience, combined with solid experience in Web Services technologies and languages such as JavaScript, HTTPS, SOAP, XML, JSON, WSDL, REST/Web Sockets, Security Protocols, and Networking, will enable you to communicate with internal and external stakeholders effectively.

As a recognized Subject Matter Expert (SME), you'll adeptly bridge the gap between technical and non-technical stakeholders, clearly articulating the operational impacts of technology decisions and their significance to business outcomes. You'll be responsible for managing key project initiatives, addressing digital transformation goals, and aligning with critical business priorities.
This critical position demands a deep understanding of enterprise architecture and development methodologies. You'll actively contribute to solutions, leveraging your expertise to tackle and resolve complex issues and process flows

We’re as proud of our working environment as we are of our market success. You’ll find all the training, opportunity, and resources you could ever want here - with all the work/life benefits you expect, and none of the micromanagement. RingCentral regularly brings home Best Place To Work awards from locations all over the world, and outstanding company ratings on Glassdoor and Comparably!

RingCentral surrounds you with world-class technology and talent, in a people-first environment built from the ground up to help you do the best work of your career. We’re not just changing the nature of communication and teamwork. We’re winning, together.

To succeed in this role you must have experience in:

TECHNICAL
Bachelor’s degree in computer science, IT, Engineering or equivalent experience.
10+ years of experience with a minimum 5 years in an Architecture or Customer Engagement Consulting role
Expertise in industry-leading contact center technologies
Enterprise ACD, CTI, and IVR experience delivering multichannel solutions.
Expertise in designing sophisticated and comprehensive Outbound dialers.
Detailed understanding of one or more CRM applications
Possesses a strong technical acumen in technical business solutions
Strong Ability to communicate technically, problem-solve, and manage critical conversations
Advanced ability to facilitate technical and business discussions
Ability to discover and document customer outcomes and software requirements
Consult on process, data, and object modeling in various application types and database environments.
A solid understanding of B2B integrations, software, and integration design patterns.
Software application development skills with experience using API RESTful / SOAP web services for application integrations and languages such as JavaScript, HTTPS, SOAP, XML, JSON, WSDL, REST/Web Sockets, Security Protocols, and Networking, will enable you to communicate with internal and external stakeholders effectively.
Technical Expertise & Solution Delivery:
 Provide expert-level knowledge in UCaaS and CCaaS, including IVR systems, email integrations, API integrations with CRM, and call flow design.
 Understand Contact Center reporting, analytics, Quality Management, and Workforce Management solutions.
 Provide technical guidance to PS Project Managers, Engineers, and customers.
 Oversee the planning, design, and implementation of Contact Center projects, including Business Requirements Documents (BRDs).
Ability to create and document a process flow diagrams
Knowledge of all phases of SLDC, support of large-scale, business-centric, and process-based applications

BUSINESS
Experience across multiple industry verticals
Ability to establish and maintain strong customer relationships and to influence others to move toward a common vision or goal
Track record of high customer satisfaction with technical aspect of projects or unique deep subject matter expertise in one or more disciplines
Experience consulting with customers to perform an initial assessment, understand and document business objectives, discuss goals and strategize on how the contact center solution can be deployed to address customer needs
Strong interpersonal writing, editing and presentation skills

BONUS EXPERIENCE
Contact center operational experience and understanding of contact center metrics and their analysis to identify improvement opportunity
Previous experience with contact center workforce management, quality management or analytics systems
In-depth knowledge of Telephony fundamentals including Carrier features, SIP, WebRTC
InContact/CXOne experience, providing technical configuration for IVR specific to CXone Studio programming

Contains bad word ""Project Manager"". Skipping this job!
",Skipped,Not Available
4271843195,https://www.linkedin.com/jobs/view/4271843195,Pending,2025-07-25 01:30:49.386141,2025-07-25 10:30:49.541165,Found a Bad Word in About Job,"
About the job
Our Mission

At Palo Alto Networks® everything starts and ends with our mission:

Being the cybersecurity partner of choice, protecting our digital way of life.

Our vision is a world where each day is safer and more secure than the one before. We are a company built on the foundation of challenging and disrupting the way things are done, and we’re looking for innovators who are as committed to shaping the future of cybersecurity as we are.

Who We Are

We take our mission of protecting the digital way of life seriously. We are relentless in protecting our customers and we believe that the unique ideas of every member of our team contributes to our collective success. Our values were crowdsourced by employees and are brought to life through each of us everyday - from disruptive innovation and collaboration, to execution. From showing up for each other with integrity to creating an environment where we all feel included.

As a member of our team, you will be shaping the future of cybersecurity. We work fast, value ongoing learning, and we respect each employee as a unique individual. Knowing we all have different needs, our development and personal wellbeing programs are designed to give you choice in how you are supported. This includes our FLEXBenefits wellbeing spending account with over 1,000 eligible items selected by employees, our mental and financial health resources, and our personalized learning opportunities - just to name a few!

At Palo Alto Networks, we believe in the power of collaboration and value in-person interactions. This is why our employees generally work full time from our office with flexibility offered where needed. This setup fosters casual conversations, problem-solving, and trusted relationships. Our goal is to create an environment where we all win with precision.



Job Description

Your Career

We are looking for a motivated Senior SRE Engineer to join the Cortex Devops Production group based at our India development ( IDC ) center. In this position you will be working side by side with Cortex Cyber Security Research group. You will be responsible for planning, executing, and reporting on various infrastructure and code projects for Cortex Cyber Security Research Group. Additionally, you will manage and execute high-pressure production maintenance tasks and address related issues.

More information about the Cortex product can be found here

Your Impact

You will take full end-to-end responsibility for the production environment of our SaaS product deployed on GCP
You will build tools for the automatic remediation of known issues
You will develop Infrastructure-as-code which will be used to orchestrate production and dev environments
You will design, build, maintain, and scale production services with thousands of Kubernetes clusters
You will secure the production environments and add in new security tools and features both internal Palo Alto Networks and other market-leading technologies
You will work closely with development teams to design and enhance software architecture to improve scalability, service reliability, cost, and performance
You will build CI pipelines and automation processes
Participate in the on-call rotation supporting the applications and infrastructure
You will research cutting-edge technologies and deploy them to production

Qualifications

Your Experience 

10+ years as DevOps/ SRE Engineer (or equal role) with a passion for technology and strong motivation and responsibility for high reliability and service level
Release automation in the past via GitLab . ArgoCD etc. 
Proficiency with code language (Python / Go - preferred)
High proficiency with Linux
GCP Proficiency in the cloud 
Proficiency with Terraform and HashiCorp tools
HPA via Karpenter / Keda with custom metrics. 
High proficiency with virtualized and containerized environments (Kubernetes and Docker)
Proficiency with CI/CD and Configuration Management (Jenkins preferred)
Proficiency with DB such as Cassandra, ScyllaDB, MemSQL, MySQL - An advantage( Optional ) 
Experience with working with internal and external customers and stakeholders
Managing a high-scale production environment
Excellent communication and interpersonal skills, ability to work and coordinate between multiple teams
Ability to grasp new technologies quickly and prioritize and multitask on multiple responsibilities

Additional Information

The Team

To stay ahead of the curve, it’s critical to know where the curve is, and how to anticipate the changes we’re facing. For the fastest-growing cybersecurity company, the curve is the evolution of cyberattacks and access technology and the products and services that dedicatedly address them. Our engineering team is at the core of our products – connected directly to the mission of preventing cyberattacks and enabling secure access to all on-prem and cloud applications. They are constantly innovating – challenging the way we, and the industry, think about Access and security. These engineers aren’t shy about building products to solve problems no one has pursued before. They define the industry, instead of waiting for directions. We need individuals who feel comfortable in ambiguity, excited by the prospect of challenge, and empowered by the unknown risks facing our everyday lives that are only enabled by a secure digital environment.

Our engineering team is provided with an unrivaled chance to create the products and practices that will support our company growth over the next decade, defining the cybersecurity industry as we know it. If you see the potential of how incredible people and products can transform a business, this is the team for you. If the prospect of affecting tens of millions of people, enabling them to work remotely securely and easily in ways never done before, thrill you - you belong with us.

Our Commitment



We’re problem solvers that take risks and challenge cybersecurity’s status quo. It’s simple: we can’t accomplish our mission without diverse teams innovating, together.

We are committed to providing reasonable accommodations for all qualified individuals with a disability. If you require assistance or accommodation due to a disability or special need, please contact <NAME_EMAIL>.

Palo Alto Networks is an equal opportunity employer. We celebrate diversity in our workplace, and all qualified applicants will receive consideration for employment without regard to age, ancestry, color, family or medical care leave, gender identity or expression, genetic information, marital status, medical condition, national origin, physical or mental disability, political affiliation, protected veteran status, race, religion, sex (including pregnancy), sexual orientation, or other legally protected characteristics.

All your information will be kept confidential according to EEO guidelines.

Contains bad word ""Legal"". Skipping this job!
",Skipped,Not Available
**********,https://www.linkedin.com/jobs/view/**********,Pending,2025-07-24 23:30:53.031904,2025-07-25 10:30:53.114707,Found a Bad Word in About Job,"
About the job
Job Position: ServiceNow Developer ITIL/ITSM 
Location: PAN.
Experience: -5+ to 10+ Years
Must have: - ServiceNow ITIL/ITSM Expert

Requirements:
ServiceNow Implementation Specialist (CIS ITSM), Admin (CSA) and Developer (CAD)
Certified on at least 1 ServiceNow Mainline certification e.g. CMDB, Discovery, Event, Workspace etc
Knowledge of ITIL methodologies and processes and modules such as Asset, Incident, Change, Problem, Knowledge, and Service Catalog, aligning IT services with business objectives.
Knowledge of Service Management (ITSM) processes, including Service Level Agreement (SLA) management and workflow monitoring, ensuring business objectives are met efficiently
Experience in Asset management and CMDB Implementation and Data Normalization.
Experience with Core module (Incident, Problem, Service Request, Knowledge, Change, Service Portal) and on Event management and Discovery Modules.
Ability to create and Modify Workflows with experience in agent workspace design and implementation.
Good at creating required information for Reporting, and Dashboards.
Hands on experience with Java Scripting.
Experience in application design, development and deployment with the Service-now platform.
Experience in migrating data from different tools into ServiceNow.

Contains bad word ""10+ years"". Skipping this job!
",Skipped,Not Available
**********,https://www.linkedin.com/jobs/view/**********,Pending,2025-07-24 17:31:12.968246,2025-07-25 10:31:13.092924,Found a Bad Word in About Job,"
About the job
Company Description
SECON Private Limited is an ISO 9001:2015, CMMI Level 3, ISO/IEC 27001:2013, and NABL ISO/IEC 17025:2017 Accredited Firm based in Bangalore, India. SECON is a Geospatial and Multi-discipline Engineering Consulting company offering comprehensive solutions in various domains such as Water Resources, Highways & Infrastructure, and geotechnical investigation. Their international experience includes projects in countries like Libya, Afghanistan, Saudi Arabia, Dubai, and with clients from Europe, the US, and Australia. SECON has been recognized for their excellence with awards like Best Services Exporter 2020 and Best Professionally Managed Company.

Role Description
· Undergo 3 – 6 Months intense Training for understanding LiDAR / Photogrammetry, Remote Sensing, Drone data acquisition, Processing for Geospatial Applications etc using modern Tools, Software / Hardware.
· Form core Team as Developers, Team Leaders, Program Managers, Project Managers and Chief Technical Officer (CTO) in the development of Geospatial related software products and Application.
· Engage in Research on any of the R&D initiatives of SECON as directed by R&D Head
· Study and develop innovative solutions and methodology mostly relating to geospatial applications.
· Undertake all tasks allocated by the Project Leader (as per the Project Plan)
· Be an aggressive member in the Project Team

Production and Processing 
· Execution of the works allotted by the Project/ Team Leader like
Planning & Designing of the Software - Deciding the overall scope of the software to be developed and designing the overall software system framework.
Software Development -Coding, testing of the software project.
Software Deployment - Deploying the developed software at the Client’s site.
Automated routine for Feature extraction using LAS file
Software Documentation - Prepare the necessary documentation related to the Software
User Training - Conduct the End User Training at client’s site
· Responsible to finish the work allotted within the scheduled period with a higher quality
· Technical advice to other team members, if necessary
· Track progress of the execution of tasks and report to the project manager on a daily basis
· Making sure that the allotted work is within the schedule as planned
· Maintain a library of all documentation relating to the execution of allocated tasks
· Escalate issues, risks and changes to the Project Manager for resolution
· Study the latest cutting edge technology and its capabilities and applications in the business market of company
· Suggestion to improve the quality of software developments/ activities and performance

Qualifications
· Post Graduation in Geospatial related Domain with consistent Academic Performance > 60%.
. Strong academic foundation in Mathematics and Physics at the higher secondary and undergraduate levels is a must
. Quick in update, keenness to learn to achieve billability within 3 – 6 Months Training.
· Good Team / People Leadership skill to Lead
· Problem solving / Analytic skills, Computer savvy
· Ability to communicate and coordinate effectively.
· Age below 25 years
· Age and qualification relaxable upto 25 years in the case of experienced candidates.

Do not apply if you’re not ready for long-term engagement or travel-based assignments. We’re building a future-ready team — and we need passionate contributors.

Contains bad word ""Team Lead"". Skipping this job!
",Skipped,Not Available
4271593137,https://www.linkedin.com/jobs/view/4271593137,Pending,2025-07-24 16:31:16.567487,2025-07-25 10:31:16.651705,Found a Bad Word in About Job,"
About the job
Qualifications and Skills for DV Positions:
Bachelor's or Masters degree in Computer Science, Electronics Engineering or equivalent practical experience
7/10+ years of hands-on experience in SystemVerilog/UVM methodology and/or C/C++ based verification
7/ 10+ years experience in IP/sub-system and/or SoC level verification based on SystemVerilog UVM/OVM based methodologies
Experience in development of UVM based verification environments from scratch
Experience in architecting and implementing Design Verification infrastructure and executing the full verification cycle7
Experience with verification of ARM/RISC-V based CPU sub-systems or SoCs
Experience with IP or integration verification along with expertise of protocols like AMBA, PCIe, DDR, USB, Ethernet
Experience in EDA tools and scripting (Python, TCL, Perl, Shell) used to build tools and flows for verification environments
Experience with revision control systems like Mercurial(Hg), Git or SVN

Contains bad word ""10+ years"". Skipping this job!
",Skipped,Not Available
4260824994,https://www.linkedin.com/jobs/view/4260824994,Pending,2025-07-24 16:31:34.182102,2025-07-25 10:31:34.289747,Found a Bad Word in About Job,"
About the job
Company Description

Sandisk understands how people and businesses consume data and we relentlessly innovate to deliver solutions that enable today’s needs and tomorrow’s next big ideas. With a rich history of groundbreaking innovations in Flash and advanced memory technologies, our solutions have become the beating heart of the digital world we’re living in and that we have the power to shape.

Sandisk meets people and businesses at the intersection of their aspirations and the moment, enabling them to keep moving and pushing possibility forward. We do this through the balance of our powerhouse manufacturing capabilities and our industry-leading portfolio of products that are recognized globally for innovation, performance and quality.

Sandisk has two facilities recognized by the World Economic Forum as part of the Global Lighthouse Network for advanced 4IR innovations. These facilities were also recognized as Sustainability Lighthouses for breakthroughs in efficient operations. With our global reach, we ensure the global supply chain has access to the Flash memory it needs to keep our world moving forward.

Job Description

We are seeking a visionary Principal Engineer as part of System Validation Infra to join our team in Bengaluru, India. In this pivotal role, you will lead the technical direction of our engineering efforts, drive innovation, and mentor our talented team of engineers. As a Principal Engineer, you will be at the forefront of designing and implementing cutting-edge solutions and monitoring infra tools and systems deployment that shape the future of our products.

Architect and design scalable, high-performance Infra software that meet business requirements and industry standards
Lead the development of technical strategies and roadmaps to guide long-term engineering initiatives
Collaborate with cross-functional teams to gather requirements and translate them into technical specifications
Provide technical leadership and mentorship to engineering teams, fostering a culture of innovation and continuous improvement
Conduct code reviews and provide constructive feedback to ensure code quality and best practices
Evaluate and recommend new technologies, tools, and methodologies to enhance our engineering capabilities
Drive the resolution of complex technical challenges and make critical design decisions
Participate in the full development lifecycle, from concept to deployment till the maintenance
Contribute to the establishment of engineering standards, best practices, and documentation
Represent the engineering team in high-level discussions with stakeholders and external partners

Qualifications

Bachelor's or Master's degree in Computer Science, Engineering, or a related field, or equivalent practical experience
8+ years of software engineering experience, with at least 3 years in a senior or lead engineering role
Thorough understanding of software architecture, design patterns, and system design principles
Proven track record of leading large to mid-scale, complex software projects from conception to successful delivery
Strong programming skills in language: Python Advanced, C++(Optional)
Basic understanding of CI/CD practices and tools, version control systems (e.g., Git), and agile methodologies
Strong understanding of performance optimization, scalability, and reliability in large to mid-scale systems
Excellent problem-solving skills with the ability to analyze complex issues and develop innovative solutions
Outstanding communication and interpersonal skills, with the ability to collaborate effectively across teams and influence technical decisions
Good knowledge of Flash Memory concepts, Protocol knowledge such as UFS or eMMC shall be an added advantage. 
Experience mentoring and developing junior engineers
Familiarity with monitoring, logging, and observability tools and practices
Strong analytical mindset with attention to detail and a data-driven approach to decision-making
Ability to balance technical excellence with pragmatic solutions to meet business objectives

Additional Information

All your information will be kept confidential according to EEO guidelines.

Contains bad word ""Principal Engineer"". Skipping this job!
",Skipped,Not Available
4275450185,https://www.linkedin.com/jobs/view/4275450185,Pending,2025-07-25 01:31:46.306696,2025-07-25 10:31:46.434421,Found a Bad Word in About Job,"
About the job
Our Mission

At Palo Alto Networks® everything starts and ends with our mission:

Being the cybersecurity partner of choice, protecting our digital way of life.

Our vision is a world where each day is safer and more secure than the one before. We are a company built on the foundation of challenging and disrupting the way things are done, and we’re looking for innovators who are as committed to shaping the future of cybersecurity as we are.

Who We Are

We take our mission of protecting the digital way of life seriously. We are relentless in protecting our customers and we believe that the unique ideas of every member of our team contributes to our collective success. Our values were crowdsourced by employees and are brought to life through each of us everyday - from disruptive innovation and collaboration, to execution. From showing up for each other with integrity to creating an environment where we all feel included.

As a member of our team, you will be shaping the future of cybersecurity. We work fast, value ongoing learning, and we respect each employee as a unique individual. Knowing we all have different needs, our development and personal wellbeing programs are designed to give you choice in how you are supported. This includes our FLEXBenefits wellbeing spending account with over 1,000 eligible items selected by employees, our mental and financial health resources, and our personalized learning opportunities - just to name a few!

At Palo Alto Networks, we believe in the power of collaboration and value in-person interactions. This is why our employees generally work full time from our office with flexibility offered where needed. This setup fosters casual conversations, problem-solving, and trusted relationships. Our goal is to create an environment where we all win with precision.



Job Description

Your Career

We are looking for a motivated Senior SRE Engineer to join the Cortex Devops Production group based at our India development ( IDC ) center. In this position you will be working side by side with Cortex Cyber Security Research group. You will be responsible for planning, executing, and reporting on various infrastructure and code projects for Cortex Cyber Security Research Group. Additionally, you will manage and execute high-pressure production maintenance tasks and address related issues.

More information about the Cortex product can be found here

Your Impact

You will take full end-to-end responsibility for the production environment of our SaaS product deployed on GCP
You will build tools for the automatic remediation of known issues
You will develop Infrastructure-as-code which will be used to orchestrate production and dev environments
You will design, build, maintain, and scale production services with thousands of Kubernetes clusters
You will secure the production environments and add in new security tools and features both internal Palo Alto Networks and other market-leading technologies
You will work closely with development teams to design and enhance software architecture to improve scalability, service reliability, cost, and performance
You will build CI pipelines and automation processes
Participate in the on-call rotation supporting the applications and infrastructure
You will research cutting-edge technologies and deploy them to production

Qualifications

Your Experience 

4+ years as DevOps Engineer (or equal role) with a passion for technology and strong motivation and responsibility for high reliability and service level
Proficiency with code language (Python / Go - preferred)
High proficiency with Linux
Proficiency in the cloud (GCP - preferred)
Proficiency with Terraform and HashiCorp tools
High proficiency with virtualized and containerized environments (Kubernetes and Docker)
Proficiency with CI/CD and Configuration Management (Jenkins preferred)
Proficiency with DB such as Cassandra, ScyllaDB, MemSQL, MySQL - An advantage (Optional) 
Experience with working with internal and external customers and stakeholders
Managing a high-scale production environment
Excellent communication and interpersonal skills, ability to work and coordinate between multiple teams
Ability to grasp new technologies quickly and prioritize and multitask on multiple responsibilities

Additional Information

The Team

To stay ahead of the curve, it’s critical to know where the curve is, and how to anticipate the changes we’re facing. For the fastest-growing cybersecurity company, the curve is the evolution of cyberattacks and access technology and the products and services that dedicatedly address them. Our engineering team is at the core of our products – connected directly to the mission of preventing cyberattacks and enabling secure access to all on-prem and cloud applications. They are constantly innovating – challenging the way we, and the industry, think about Access and security. These engineers aren’t shy about building products to solve problems no one has pursued before. They define the industry, instead of waiting for directions. We need individuals who feel comfortable in ambiguity, excited by the prospect of challenge, and empowered by the unknown risks facing our everyday lives that are only enabled by a secure digital environment.

Our engineering team is provided with an unrivaled chance to create the products and practices that will support our company growth over the next decade, defining the cybersecurity industry as we know it. If you see the potential of how incredible people and products can transform a business, this is the team for you. If the prospect of affecting tens of millions of people, enabling them to work remotely securely and easily in ways never done before, thrill you - you belong with us

Our Commitment



We’re problem solvers that take risks and challenge cybersecurity’s status quo. It’s simple: we can’t accomplish our mission without diverse teams innovating, together.

We are committed to providing reasonable accommodations for all qualified individuals with a disability. If you require assistance or accommodation due to a disability or special need, please contact <NAME_EMAIL>.

Palo Alto Networks is an equal opportunity employer. We celebrate diversity in our workplace, and all qualified applicants will receive consideration for employment without regard to age, ancestry, color, family or medical care leave, gender identity or expression, genetic information, marital status, medical condition, national origin, physical or mental disability, political affiliation, protected veteran status, race, religion, sex (including pregnancy), sexual orientation, or other legally protected characteristics.

All your information will be kept confidential according to EEO guidelines.


Is role eligible for Immigration Sponsorship? No. Please note that we will not sponsor applicants for work visas for this position.

Contains bad word ""Legal"". Skipping this job!
",Skipped,Not Available
**********,https://www.linkedin.com/jobs/view/**********,Pending,2025-07-24 15:31:52.211470,2025-07-25 10:31:52.328132,Found a Bad Word in About Job,"
About the job
Company Description

The future. It’s on you. You & Western Digital.

We’ve been storing the world’s data for more than 50 years. Once, it was the most important thing we could do for data. Now we’re helping the world capture, preserve, access and transform data in a way only we can.

The most game-changing companies, consumers, professionals, and governments come to us for the technologies and solutions they need to capture, preserve, access, and transform their data.

But we can’t do it alone. Today’s exceptional data challenges require your exceptional skills. It’s You & Us. Together, we’re the next big thing in data. Western Digital® data-centric solutions are found under the G-Technology™, HGST, SanDisk®, Tegile™, Upthere™, and WD® brands.

Western Digital is an equal opportunity employer. Western Digital does not discriminate on the basis of race, color, ancestry, religion (including religious dress and grooming standards), sex (including pregnancy, childbirth or related medical conditions, breastfeeding or related medical conditions), gender (including a person’s gender identity, gender expression, and gender-related appearance and behavior, whether or not stereotypically associated with the person’s assigned sex at birth), age, national origin, sexual orientation, medical condition, marital status (including domestic partnership status), physical disability, mental disability, medical condition, genetic information, protected medical and family care leave, Civil Air Patrol status, military and veteran status, or other legally protected characteristics. We also prohibit harassment of any individual on any of the characteristics listed above. Our non-discrimination policy applies to all aspects of employment. We comply with the laws and regulations set forth in the ""Equal Employment Opportunity is the Law"" poster.

Federal and state laws require employers to provide reasonable accommodation to qualified individuals with disabilities. Please tell us if you require a reasonable accommodation to apply for a job or to perform your job. Examples of reasonable accommodation include making a change to the application process or work procedures, providing documents in an alternate format, using a sign language interpreter, or using specialized equipment. If you need any accommodation or assistance with our career site, please contact <NAME_EMAIL>.

Western Digital participates in the E-Verify program in the US. For more information click here. Este empleador participa in E-Verify.

Job Description

ESSENTIAL DUTIES AND RESPONSIBILITIES:

Need experienced Validation engineers who understand embedded NAND system design and firmware algorithms in order to create a test validation plans and implement them in modern object oriented languages.

Work closely with the system architects and the firmware team to develop Validation plans, test bench and test cases
Develop an overall validation strategy including defining validation infrastructure and validation methodology
Debug the firmware and expose design issues
Define and design functional tests required to meet customer needs
Review SanDisk UFS/eMMC embedded NAND validation requirements and influence future SanDisk product design for debug and test
Work with customers to understand field bugs and to enhance the validation coverage
Interface with all key stakeholders to ensure product validation meets customer expectations and needs

Qualifications

REQUIRED:

Bachelor or Master in Computer Science, Computer/Electronics Engineering or equivalent required.
Minimum 5+ years in storage related segment
Capable of quickly learning hardware, systems, tools, and methodologies. 

Technical And Analytical Skills Required

In depth understanding of firmware algorithms used in any NAND Flash based storage devices (SSD, eMMC, UFS, SD, USB Flash drives) or other storage devices
Knowledge in any Host protocols like as UFS, eMMC, SCSI, SATA, PCIe, NVMe is an added advantage
Experienced and familiar with firmware development, Integration and validation
Knowledgeable on product and quality standards and relevant host protocols, in particular eMMC and UFS.
Experience in the area of Grey box or White box.
Test coverage methods for real-time embedded systems, especially storage systems and/or NAND storage
Able to methodically root cause complex failure mechanism
Strong programming knowledge & debugging skills in Python, C and C++ , Shell.
Soft skills, Excellent written and verbal skills, be a Team player
Able to develop key relationships
Able to elect requirements from all stakeholders
Be able to work in pressure for quick resolution and delivery.
Knowledge on Mobile/Compute/Industrial OS and Filesystem
Experience in the area of digital and analog design

Additional Information

Because Western Digital thrives on the power of diversity and is committed to an inclusive environment where every individual can thrive through a sense of belonging, respect, and contribution, we are committed to giving every qualified applicant and employee an equal opportunity. Western Digital does not discriminate against any applicant or employee based on their protected class status and complies with all federal and state laws against discrimination, harassment, and retaliation, as well as the laws and regulations set forth in the ""Equal Employment Opportunity is the Law"" poster.

Part of creating a diverse and inclusive workplace includes ensuring that all qualified applicants and employees are provided equal consideration for any available opportunity. Western Digital is committed to offering opportunities to applicants with a disability. If you need a reasonable accommodation, email <NAME_EMAIL> your email, please include a description of the specific accommodation you are requesting as well as the job title and requisition number of the position for which you are applying.

Contains bad word ""Legal"". Skipping this job!
",Skipped,Not Available
4271538684,https://www.linkedin.com/jobs/view/4271538684,Pending,2025-07-24 10:33:17.792918,2025-07-25 10:33:17.965452,Found a Bad Word in About Job,"
About the job
Trelleborg is a world leader in engineered polymer solutions for almost every industry on the planet. And we are where we are because our talents brought us here. By specializing in the polymer engineering that makes innovation and application possible, Trelleborg works closely with leading industry brands to accelerate their performance, drive their business forward—and along the way, shape the industry and progress that will benefit humankind in the exciting years ahead. Our people are Shaping Industry from the Inside. Why don´t you join us? 
Are you a talent looking to build business skills, gain experience, and take on exciting challenges? Grow your career with Trelleborg and start shaping the industry from the inside.

We are looking for highly skilled and experienced Senior Software Engineer - Development to join our IT Innovation Campus at Bangalore location. The position will assist for architecting and developing high-performance, scalable software solutions, mentoring junior engineers, and collaborating across teams to build impactful products.

Your Responsibilities:
Create scalable and maintainable architectures to solve business problems
Design, develop, test, and maintain software applications, services, and
Mentor and guide junior engineers, providing technical support and sharing best
Lead and participate in writing clean, maintainable, and high-quality Ensure code is well- documented and adheres to industry best practices.
Conduct regular code reviews to ensure code quality and to foster a culture of Ensure that automated tests are in place and that systems are thoroughly tested.
Work closely with cross-functional teams, including product managers, designers, and QA engineers, to gather requirements and deliver robust solutions.
Investigate, analyze, and resolve complex technical Implement performance tuning and optimization strategies as needed.
Contribute to the product vision and continuously improve existing systems, applications, and services.

What You'll Bring:
Bachelor’s or master’s
5+ years of professional experience in software engineering, with at least 2-3 years in a senior or lead role.
Strong proficiency skills required - HTML5/CSS3, JavaScript, TypeScript, React JS, Java/Jee, Spring Boot, Spring Data JPA, Relational Database (Oracle/MySQL DB), Microservices architecture (e.g., Java, Python, JavaScript) with a deep understanding of software development fundamentals.
Strong experience using Git, Maven or other version control Systems
Strong analytical skills and a demonstrative ability to solve complex technical problems.
Excellent written and verbal communication skills, with the ability to explain complex technical concepts to non-technical stakeholders.
Solid experience in designing complex, scalable systems and web applications, including microservices and cloud-native architectures. Familiarity with Agile and Scrum development
Experience with relational (SQL) or NoSQL databases (e.g., MySQL, PostgreSQL, MongoDB).
Familiarity with CI/CD pipelines, automation, and tools like Jenkins, GitLab, or
Excellent analytical and troubleshooting skills with a passion for solving complex challenges.
Expertise in modern web UI frameworks/libraries (e.g., React, Angular, Vue.js) and full-stack development.

Why Work with Us?
At Trelleborg, your career progression and personal development are of utmost importance to us. In our vibrant and dynamic work setting, every contribution you make is recognized and your professional aspirations are actively fostered. Committed to innovation, excellence, and ecological sustainability, we ensure that your efforts contribute not just to our organizational achievements but also to global technological and data management advancements. Seize this chance to make a meaningful difference at Trelleborg, where you face stimulating challenges, your growth is certain, and your career can flourish.

Application Process
Trelleborg is an equal opportunity employer! We celebrate diversity and are committed to creating an inclusive environment for all employees. We will ensure that individuals with disabilities are provided reasonable accommodation to participate in the job application or interview process, to perform crucial job functions, and to receive other benefits and privileges of employment. Please contact us to request accommodation.

At Trelleborg, our people are #ShapingIndustryfromtheInside#

Feel free to contact our HR Team for any questions:

Ashwini @ <EMAIL>

Contains bad word ""Senior Software Engineer"". Skipping this job!
",Skipped,Not Available
4271890234,https://www.linkedin.com/jobs/view/4271890234,Pending,2025-07-25 09:52:21.124646,2025-07-25 10:33:21.232607,Found a Bad Word in About Job,"
About the job
Company Description
Chitrayana Private Limited is a content company specializing in social media marketing and website building. We embrace new ideas and possibilities to create engaging content. Our dynamic team is dedicated to producing high-quality content that resonates with diverse audiences.

Role Description
This is a full-time on-site role for a WordPress Developer located in Bengaluru. The WordPress Developer will be responsible for both back-end and front-end development, including creating WordPress themes and plugins. Day-to-day tasks include building responsive websites, designing and implementing new features and functionalities, and ensuring high performance and availability, and managing all technical aspects of the CMS.

Qualifications
Proficiency in Back-End Web Development
Experience in Front-End Development and Responsive Web Design
Skills in Web Design and Web Development
Strong understanding of PHP, HTML, CSS, JavaScript, and jQuery
Familiarity with the Genesis, Divi, and Elementor themes is a plus
Excellent problem-solving and troubleshooting skills
Strong communication and teamwork skills
Bachelor's degree in Computer Science or a related field

Contains bad word ""Marketing"". Skipping this job!
",Skipped,Not Available
4275506837,https://www.linkedin.com/jobs/view/4275506837,Pending,2025-07-25 07:33:24.747633,2025-07-25 10:33:24.885944,Required experience is high,"
About the job
About Us
MyRemoteTeam, Inc is a fast-growing distributed workforce enabler, helping companies scale with top global talent. We empower businesses by providing world-class software engineers, operations support, and infrastructure to help them grow faster and better.

Job Title: Senior Java Spring Boot Developer
Experience: 6+ Years
Location: Mysore/Bangalore

Job Description:
We are seeking an experienced Senior Java Spring Boot Developer with 6+ years of hands-on experience in building scalable, high-performance microservices using Java, Spring Boot, and Spring JPA. The ideal candidate will have strong expertise in designing and developing RESTful APIs, microservices architecture, and cloud-native applications.
As part of our team, you will work on enterprise-grade applications, collaborate with cross-functional teams, and contribute to the full software development lifecycle.

Mandatory Skills:
✔ 6+ years of Java development (Java 8/11/17).
✔ Strong Spring Boot & Spring JPA experience.
✔ Microservices architecture (design, development, deployment).
✔ RESTful API development & integration.
✔ Database expertise (SQL/NoSQL – PostgreSQL, MySQL, MongoDB).
✔ Testing frameworks (JUnit, Mockito).
✔ Agile methodologies & CI/CD pipelines.

Key Responsibilities:

Design & Development:
Develop high-performance, scalable microservices using Spring Boot.
Design and implement RESTful APIs following best practices.
Use Spring JPA/Hibernate for database interactions (SQL/NoSQL).
Implement caching mechanisms (Redis, Ehcache) for performance optimization.

Microservices Architecture:
Build and maintain cloud-native microservices (Docker, Kubernetes).
Integrate with message brokers (Kafka, RabbitMQ) for event-driven systems.
Ensure fault tolerance, resilience, and scalability (Circuit Breaker, Retry Mechanisms).

Database & Performance:
Optimize database queries (PostgreSQL, MySQL, MongoDB).
Implement connection pooling, indexing, and caching strategies.
Monitor and improve application performance (JVM tuning, profiling).

Testing & Quality Assurance:
Write unit & integration tests (JUnit, Mockito, Test Containers).
Follow TDD/BDD practices for robust code quality.
Perform code reviews and ensure adherence to best practices.

DevOps & CI/CD:
Work with Docker, Kubernetes, and cloud platforms (AWS/Azure).
Set up and maintain CI/CD pipelines (Jenkins, GitHub Actions).
Automate deployments and monitoring (Prometheus, Grafana).

Collaboration & Agile:
Work in Agile/Scrum teams with daily standups, sprint planning, and retrospectives.
Collaborate with frontend, QA, and DevOps teams for seamless delivery.

Experience required 6 > Current Experience 5. Skipping this job!
",Skipped,Not Available
4269656770,https://www.linkedin.com/jobs/view/4269656770,Pending,2025-07-24 21:33:28.322623,2025-07-25 10:33:28.444909,Found a Bad Word in About Job,"
About the job
We are hiring for Full Stack Developer | Gurgaon/Bangalore

Role: Software Engineer - Full Stack
Location: Gurgaon/Bangalore (Hybrid)
Experience-1-3 Years

Mandate Skills- Node JS, Angular, SQL, Java & Spring Boot

Job Description: 
As a Full Stack Developer, you will play a pivotal role in developing and maintaining our web applications (Angular, NodeJS) and backend services (Java, Spring Boot). You will work closely with cross-functional teams to ensure the seamless development and integration of front-end and back-end components, delivering exceptional user experiences. The ideal candidate will have a strong foundation in software development, a keen eye for detail, and a passion for keeping up with emerging technologies.

 Responsibilities:
Collaborate with product managers, UI/UX designers, technical leads, and fellow developers to design and implement robust software solutions.
Participating in daily standup, sprint planning, retrospective meetings during project implementation phase.
Develop responsive and user-friendly front-end interfaces using Angular, ensuring optimal performance across various devices and browsers.
Design and implement RESTful APIs and back-end services using Java/SpringBoot to support front-end functionalities.
Write unit, integration, and end-to-end tests to ensure application quality and performance.
Work with databases and data models to ensure efficient data storage, retrieval, and manipulation.

Skills:
Proven experience (min 1+ years) as a Full Stack Developer with hands-on expertise in Angular, NodeJS and Java with Spring Boot.
Familiarity with the Angular framework and design/architectural patterns (e.g. Microservices, Model-View-Controller (MVC) and Entity framework)
Strong understanding of web development fundamentals, including HTML, CSS, and JavaScript.
Proficiency in designing and consuming RESTful APIs.
Solid knowledge of database systems, SQL, and data modeling.
Familiarity with version control systems (e.g., Git) and agile development methodologies.
Strong problem-solving skills and the ability to work effectively in a collaborative team environment.
Familiarity with Docker, Kubernetes,
Exposure to testing frameworks like Jasmine, Karma (Angular), JUnit, Mockito (Java).
Excellent communication skills, both written and verbal.
Experience using IDE tools like Visual Studio Code and Eclipse.
Self-driven, collaborative, and passionate about clean, maintainable code.

Education and Certification
Bachelor’s degree in Computer Science, Engineering, or related field.
Certification in AWS Certified: Developer Associate or equivalent is a plus.

Interested candidates can send their <NAME_EMAIL>

Contains bad word ""Technical Lead"". Skipping this job!
",Skipped,Not Available
4271336979,https://www.linkedin.com/jobs/view/4271336979,Pending,2025-07-24 12:33:42.864295,2025-07-25 10:33:43.006271,Found a Bad Word in About Job,"
About the job
About Company

At Opkey, we are disrupting the space of ERP transformation testing by building an AI-powered No Code Testing platform for Enterprise business applications (like Oracle Fusion Cloud, SAP S4Hana, SAP, Workday, Salesforce, and the likes). Opkey is a fast-growing VC-backed continuous end-to-end test automation software company headquartered in Dublin, California, with additional offices in Pittsburgh (opened in 2022), NYC (opened in 2022), and India (Noida & Bangalore). With the test automation market growing 20% annually, it's estimated to reach $50 billion by 2026. Trusted by 250+ enterprise customers, including GAP, Pfizer, and KPMG.

Job Description

We are seeking a motivated Trainee Automation Test Engineer with a basic understanding of automation testing concepts. This role is ideal for freshers or candidates with minimal experience who are eager to learn and grow in automation testing.

Key Responsibilities

Learn and implement automation testing frameworks and tools.
Develop and maintain automated test scripts using XPath for web applications.
Collaborate with the development and testing teams to understand requirements and test scenarios.
Execute automated test cases and analyze results.
Identify and report bugs, ensuring high software quality.
Continuously improve automation scripts and frameworks.

Required Skills

Understanding of Selenium WebDriver or other automation tools (preferred).
Familiarity with any programming language like Java, Python, or JavaScript (preferred).
Strong analytical and problem-solving skills.
Eagerness to learn and work in a fast-paced environment.

Preferred Qualifications

Bachelor's degree in computer science, IT, or a related field.
Internship or academic projects in test automation are a plus.
Basic knowledge of API testing and CI/CD pipelines (optional).

What We Offer

Hands-on training in automation testing and XPath.
Opportunity to work with experienced mentors.
Career growth opportunities in automation and QA.

Skills: api testing,java,automation,testing,selenium webdriver,python,selenium,ci/cd pipelines,xpath,javascript

Contains bad word ""Sales"". Skipping this job!
",Skipped,Not Available
4275394243,https://www.linkedin.com/jobs/view/4275394243,Pending,2025-07-24 10:33:55.955853,2025-07-25 10:33:56.826798,Required experience is high,"
About the job
Job Title: Full-Stack Developer
Experience: 6+ years
Top Skills: Java, Spring, React, SQL, TypeScript
Work Mode: Hybrid - 3 days from the office
Work Location: Marathahalli, Bangalore.

Job Summary:
We âre looking for a Full-Stack Developer with solid experience in Java, React, and SQL. You âll be part of a collaborative team, building and enhancing responsive web applications using modern technologies.
Key Responsibilities:
Build full-stack applications using Java, React, and GraphQL.
Create responsive web interfaces that interact with backend APIs.
Communicate clearly with team members across functions.
Take part in Agile practices like sprint planning, reviews, and retrospectives.
Contribute to improving the development process and sharing best practices.

Must-Have Skills:
Strong Java and Spring framework knowledge.
Proficiency in React and TypeScript.
Solid SQL and API integration experience.
Understanding of GraphQL and frontend technologies like JavaScript.
Good communication and teamwork abilities.

Good to Have:
Exposure to Magnolia CMS or Next.js.
Experience working with open-source projects.
Familiarity with Agile development processes.

Experience required 6 > Current Experience 5. Skipping this job!
",Skipped,Not Available
4271807724,https://www.linkedin.com/jobs/view/4271807724,Pending,2025-07-24 18:33:59.666614,2025-07-25 10:33:59.759472,Found a Bad Word in About Job,"
About the job
Job Description -

Strong in Teradata ETL architecture & SQL development. · Independently analyse, develop, test & execute assigned modules with ownership attitude.
The role focuses on developing and maintaining Teradata-based data warehousing solutions. Responsibilities include writing SQL queries, designing database structures, and optimizing performance for large datasets.
Continuously learning about the Teradata platform and related technologies.
Working with a team of developers, business analysts, and other stakeholders to deliver data warehousing solutions.
Participating in testing activities, including unit testing, system integration testing (SIT), and regression testing.
Working with data models, including Teradata FSLDM (Financial Services Logical Data Model), and creating mapping documents.

Contains bad word ""Business Analyst"". Skipping this job!
",Skipped,Not Available
**********,https://www.linkedin.com/jobs/view/**********,Pending,2025-07-24 15:34:05.619360,2025-07-25 10:34:05.804098,Found a Bad Word in About Job,"
About the job
Job Title: Senior Software Engineer – React Native 
Location: Bangalore 
Experience: 4–6 years 
Department: Engineering 
Type: Full-time

About the Role: 

We are looking for a skilled and passionate Senior React Native Developer to join our mobile team. As an SSE, you will be responsible for designing, building, and maintaining cross-platform mobile applications with React Native. You will collaborate with product managers, designers, and backend engineers to deliver high-quality, scalable apps used by thousands (or millions) of users. 

Key Responsibilities:

 ● Design and develop high-performance mobile apps using React Native for both iOS and Android platforms. 
● Own the end-to-end delivery of new features and improvements. 
● Collaborate with cross-functional teams including UI/UX designers, backend engineers, and QA. ● Optimize app performance, debug issues, and ensure app stability and responsiveness. 
● Work with native modules and third-party libraries when required. 
● Participate in code reviews, mentor junior developers, and maintain coding standards. 
● Stay up-to-date with the latest trends in mobile development and continuously explore new technologies/tools.

Required Skills & Qualifications:

● 4–6 years of experience in mobile app development, with 2+ years in React Native. 
● Strong understanding of JavaScript (ES6+), TypeScript, and mobile app architectures. 
● Experience with React Navigation, Redux/MobX, Context API, and other common libraries. 
● Proficient in integrating RESTful APIs and GraphQL endpoints. 
● Experience with native Android/iOS modules and bridging (Java/Swift/Kotlin/Objective-C) is a plus. 
● Familiarity with app deployment on Play Store and App Store. 
● Good understanding of CI/CD pipelines, code versioning (Git), and debugging tools (e.g., Flipper). 
● Strong knowledge of performance tuning, memory optimization, and UI responsiveness. 
● Bachelor’s degree in Computer Science, Engineering, or related field.

Preferred Skills (Good to Have): 

● Experience with Expo and bare workflow. 
● Knowledge of Firebase, push notifications, in-app purchases, and analytics tools. 
● Familiarity with Unit Testing / E2E Testing frameworks (e.g., Jest, Detox). 
● Experience working in Agile/Scrum teams.

Contains bad word ""Senior Software Engineer"". Skipping this job!
",Skipped,Not Available
4271552327,https://www.linkedin.com/jobs/view/4271552327,Pending,2025-07-24 12:34:12.168585,2025-07-25 10:34:12.318112,Found a Bad Word in About Job,"
About the job
Employer : Global Product Company - Established 1969

Why Join Us?
Be part of a global product company with over 50 years of innovation.
Work in a collaborative and growth-oriented environment.
Help shape the future of digital products in a rapidly evolving industry.

Job Title : Principal / Senior Software Engineer
Job Location : Marathahalli , Bangalore(Hybrid)
Exp Range : 10 to 20 years

Top Skills : Scala ,Java ,Sql , Cloud and Big Data.


Required job skills: 
 Strong software design skills with a deep understanding of design patterns and performance optimization. 
Expertise in writing high-quality, well-structured Scala code with an emphasis on functional programming and test-driven development. 
Ability to produce clear, concise, and organized documentation. 
Knowledge of Amazon cloud computing services (Aurora MySQL, DynamoDB, EMR, Lambda, Step Functions, and S3). 
Excellent communication skills and the ability to collaborate effectively with team members of varying technical backgrounds. 
Proficiency in conducting detailed code reviews focused on improving code quality and mentoring developers. 
Familiarity with software engineering and project management tools. 
Commitment to following security protocols and best practices in data governance. 
Capability to construct KPIs and use metrics for continuous process improvement 
 Minimum qualifications:  
10+ years of experience designing and developing enterprise-level software solutions. 
10+ years of experience with large volume data processing and Big Data tools such as Apache Spark, Scala, and Hadoop. 
5+ years of experience developing Scala/Java applications and microservices using Spring Boot. 
5+ years of experience working with SQL and relational databases. 
2+ years of experience working within Agile/Scrum environments.

Contains bad word ""Senior Software Engineer"". Skipping this job!
",Skipped,Not Available
4268786150,https://www.linkedin.com/jobs/view/4268786150,Pending,2025-07-19 10:34:31.539774,2025-07-25 10:34:31.709922,Found a Bad Word in About Job,"
About the job
Java backend developer 
 Job Summary
We’re looking for a Java backend developer to support our team in Bangalore .This role offers the opportunity to work on meaningful projects, collaborate with talented colleagues, and contribute to the success of a growing company. If you’re someone who takes initiative, values continuous learning, and thrives in a collaborative setting, we’d love to hear from you.
Role Description

Experience in building Cloud Native applications from a Domain driven design and micro-services architecture perspective
Expertise in defining physical data models, write maintainable & testable code that is consistent with micro-service architecture principles
Experience in working with fully automated CI / CD pipelines, support software solutions that are customer focused & highly secure.
Know-how in defining end-to-end application architecture/development, Performance, Security, testing.
Must Have Skills: 
Hands-on Experience in Micro Services Design and Development - adhering to Azure Cloud principles.
Implementation knowledge of managing asynchronous communication using Kafka or other JMS broker solutions to achieve similar result.
Hands on experience with coding large-scale feature sets at every level of the stack, from the database till APIs.
Java (17 and above),
Spring boot,
Spring-core,
Hibernate,
RESTful,
GraphQL,
Azure AKS
Maven,
GIT, Azure Repo
mockito, jmockit, jacoco, etc
This role requires a wide variety of strengths and capabilities, including:
BS/BA degree or equivalent experience
Ability to work collaboratively in teams and develop meaningful relationships to achieve
Understanding of software skills such as business analysis, development, maintenance, and software improvement.
RDBMS experience preferably with Postgres/Mysql
Strong believer of code quality and Well versed with Test Driven Development/Behaviour
Exposure/competence with Agile Development approach
Working experience with continuous integration/development using CI/CD
 Please send the update CV along with the following <NAME_EMAIL>
If you are not interested, please refer to your friends.

Full Name:
Current Location:
Visa Status:
Total years of experience:
Relevant years of experience:
Current salary:
Expected Salary:
Notice period:
Reason for leaving:


About CLPS RiDiK
RiDiK is a global technology solutions provider and a subsidiary of CLPS Incorporation (NASDAQ: CLPS), delivering cutting-edge end-to-end services across banking, wealth management, and e-commerce. With deep expertise in AI, cloud, big data, and blockchain, we support clients across Asia, North America, and the Middle East in driving digital transformation and achieving sustainable growth. Operating from regional hubs in 10 countries and backed by a global delivery network, we combine local insight with technical excellence to deliver real, measurable impact. Join RiDiK and be part of an innovative, fast-growing team shaping the future of technology across industries.

Thanks & Regards,
Rituparna Das
IT Recruiter
CLPS Inc.
| India HP/Whatsapp: +91 **********
| India Office: + 65 ********

Contains bad word ""Blockchain"". Skipping this job!
",Skipped,Not Available
**********,https://www.linkedin.com/jobs/view/**********,Pending,2025-07-19 10:34:36.711524,2025-07-25 10:34:36.794339,Found a Bad Word in About Job,"
About the job
Optimum Data Analytics is a strategic technology partner delivering reliable turnkey AI solutions. Our streamlined approach to development ensures high-quality results and client satisfaction. We bring experience and clarity to organizations, powering every human decision with analytics & AI.

Our team consists of statisticians, computer science engineers, data scientists, and product managers. With expertise, flexibility, and cultural alignment, we understand the business, analytics, and data management imperatives of your organization. Our goal is to change how AI/ML is approached in the service sector and deliver outcomes that matter.

We provide best-in-class services that increase profit for businesses and deliver improved value for customers, helping businesses grow, transform, and achieve their objectives

Job Title : Full Stack Developer – React.js, Python, SQL & NoSQL

Location : Bangalore

Experience : 4-7 years

Required Skills

Frontend React.js with Shadcn, Redux, HTML5, CSS3, tailwind CSS

Backend Python, Flask / FastAPI

Databases SQL Server, MongoDB

API Integration REST APIs

Version Control Git, GitHub

Testing Jest, PyTest Postman, Swagger

Deployment (Plus) Docker, CI/CD, Linux scripting

Skills: ci/cd,tailwind css,mongodb,sql,html5,fast api,shadcn,fastapi,python,postman,redux,jest,git,linux scripting,rest apis,docker,pytest,swagger,backend python,css3,react.js,github,sql server,flask

Contains bad word ""Product Manager"". Skipping this job!
",Skipped,Not Available
4275513414,https://www.linkedin.com/jobs/view/4275513414,Pending,2025-07-25 07:34:44.987686,2025-07-25 10:34:45.157190,Found a Bad Word in About Job,"
About the job
About Us
MyRemoteTeam, Inc is a fast-growing distributed workforce enabler, helping companies scale with top global talent. We empower businesses by providing world-class software engineers, operations support, and infrastructure to help them grow faster and better.

Job Title: Full Stack Developer – Java + React
Experience: 5+ Years

Job Summary:
We are looking for a talented and experienced Full Stack Developer (Java + React) with a strong background in building scalable, high-quality, and high-performance web applications. The ideal candidate will have hands-on experience in Java, Spring Boot, Microservices architecture, and React.js.

Key Responsibilities:
Design, develop, and maintain scalable backend services using Java, Spring Boot, and Microservices.
Develop intuitive and responsive frontend interfaces using React.js.
Collaborate with product managers, UI/UX designers, and other developers to understand requirements and translate them into technical solutions.
Ensure code quality through unit testing, code reviews, and best practices.
Integrate APIs and work with third-party services as needed.
Optimize application performance and scalability.
Participate in Agile/Scrum development cycles, including sprint planning and daily stand-ups.
Troubleshoot and resolve production issues in a timely manner.

Required Skills:
5+ years of experience in Java development
Strong experience with Spring Boot and building RESTful APIs
Solid understanding of Microservices architecture
2+ years of hands-on experience with React.js, JavaScript, HTML5, and CSS3
Experience working with databases like MySQL, PostgreSQL, or MongoDB
Familiarity with version control systems (Git)
Understanding of CI/CD pipelines and deployment processes
Good problem-solving skills and attention to detail

Contains bad word ""Product Manager"". Skipping this job!
",Skipped,Not Available
**********,https://www.linkedin.com/jobs/view/**********,Pending,2025-07-25 10:25:28.492005,2025-07-25 10:36:28.575429,Found a Bad Word in About Job,"
About the job
Company Description
 Whitestone Software Solutions, formed in 2019, provides software products and services for Banking, Financial Services, Micro-Finance, and Government verticals globally. The company has earned a unique reputation for its solutions and delivery excellence, serving more than 20 customers in 5 countries across 2 continents. Whitestone also offers application development, maintenance, consulting, and IT outsourcing services.
 Role Description
 This is a full-time hybrid role for a Java DevOps position located in Bengaluru, with some work-from-home flexibility. The Java DevOps professional will be responsible for software development, continuous integration tasks, integrating various systems, managing Kubernetes environments, and handling Linux-based systems. The role involves collaborating with different teams to ensure efficient development and operational workflows.
 Qualifications
  Proficiency in Software Development and Integration
Experience with Continuous Integration and Kubernetes
Strong Linux-based system management skills
Excellent problem-solving and analytical skills
Ability to work independently and as part of a team
Bachelor’s degree in Computer Science, Information Technology, or a related field
Experience in the financial services sector is a plus

Contains bad word ""Finance"". Skipping this job!
",Skipped,Not Available
4275539160,https://www.linkedin.com/jobs/view/4275539160,Pending,2025-07-25 09:52:32.997044,2025-07-25 10:36:33.109461,Found a Bad Word in About Job,"
About the job
Role: Salesforce Marketing Cloud Developer
Location: Bangalore | Hyderabad | Pune | Gurgaon | Noida | Chennai
Experience: 2 to 8 Years
Notice Period: Immediate Joiners Only

Job Description:
PRIMARY RESPONSIBILITIES
The individual should have extensive knowledge and experience in the following areas: 

Solid hands-on experience working with Marketing Cloud (Email, Mobile, Automation Studio as well as content, contact & journey builder)
Experience on JavaScript, HTML & CSS
Expertise in Business Requirement gathering, Analysis & conceptualizing high-level framework & design 
Exposure to SFDC and its integration with SFMC
Conceptualize integration via API (inbound and outbound), sFTP and with middleware (good to have)
Experience in REST & SOAP APIs, knowledge of Salesforce limits involved during Integration
Should have experience with data modeling and solid on data transformation using SQLs
Strong experience in designing and working with large scale multi cloud applications 
Excellent consulting skills, oral and written communication, and analytical skills 
Marketing Cloud Email Specialist & Marketing Cloud Developer certifications

SECONDARY REQUIREMENTS
, Marketing Cloud Consultant
Knowledge Social Studio, Advertisement Studio & Interaction Studio
Incident and problem management skills
Ability to provide necessary coaching to bring lesser experienced up to speed on the technology 


PERSONAL ATTRIBUTES
Strong written and verbal communication skills

Contains bad word ""Sales"". Skipping this job!
",Skipped,Not Available
4275357474,https://www.linkedin.com/jobs/view/4275357474,Pending,2025-07-25 09:36:37.047562,2025-07-25 10:36:37.191932,Found a Bad Word in About Job,"
About the job
We're looking for a CCaaS Application Architect to be a pivotal part of our client's success. In this role, you will cultivate strong relationships with clients, gaining a deep understanding of their unique business and technical needs. Your expertise in Contact Center solutions and technical design will be vital in designing and engineering innovative solutions that effectively leverage our AI tools and integrate seamlessly with clients' CRM systems. 

Your Contact Center experience, combined with solid experience in Web Services technologies and languages such as JavaScript, HTTPS, SOAP, XML, JSON, WSDL, REST/Web Sockets, Security Protocols, and Networking, will enable you to communicate with internal and external stakeholders effectively.

As a recognized Subject Matter Expert (SME), you'll adeptly bridge the gap between technical and non-technical stakeholders, clearly articulating the operational impacts of technology decisions and their significance to business outcomes. You'll be responsible for managing key project initiatives, addressing digital transformation goals, and aligning with critical business priorities.
This critical position demands a deep understanding of enterprise architecture and development methodologies. You'll actively contribute to solutions, leveraging your expertise to tackle and resolve complex issues and process flows

We’re as proud of our working environment as we are of our market success. You’ll find all the training, opportunity, and resources you could ever want here - with all the work/life benefits you expect, and none of the micromanagement. RingCentral regularly brings home Best Place To Work awards from locations all over the world, and outstanding company ratings on Glassdoor and Comparably!

RingCentral surrounds you with world-class technology and talent, in a people-first environment built from the ground up to help you do the best work of your career. We’re not just changing the nature of communication and teamwork. We’re winning, together.

To succeed in this role you must have experience in:

TECHNICAL
Bachelor’s degree in computer science, IT, Engineering or equivalent experience.
10+ years of experience with a minimum 5 years in an Architecture or Customer Engagement Consulting role
Expertise in industry-leading contact center technologies
Enterprise ACD, CTI, and IVR experience delivering multichannel solutions.
Expertise in designing sophisticated and comprehensive Outbound dialers.
Detailed understanding of one or more CRM applications
Possesses a strong technical acumen in technical business solutions
Strong Ability to communicate technically, problem-solve, and manage critical conversations
Advanced ability to facilitate technical and business discussions
Ability to discover and document customer outcomes and software requirements
Consult on process, data, and object modeling in various application types and database environments.
A solid understanding of B2B integrations, software, and integration design patterns.
Software application development skills with experience using API RESTful / SOAP web services for application integrations and languages such as JavaScript, HTTPS, SOAP, XML, JSON, WSDL, REST/Web Sockets, Security Protocols, and Networking, will enable you to communicate with internal and external stakeholders effectively.
Technical Expertise & Solution Delivery:
 Provide expert-level knowledge in UCaaS and CCaaS, including IVR systems, email integrations, API integrations with CRM, and call flow design.
 Understand Contact Center reporting, analytics, Quality Management, and Workforce Management solutions.
 Provide technical guidance to PS Project Managers, Engineers, and customers.
 Oversee the planning, design, and implementation of Contact Center projects, including Business Requirements Documents (BRDs).
Ability to create and document a process flow diagrams
Knowledge of all phases of SLDC, support of large-scale, business-centric, and process-based applications

BUSINESS
Experience across multiple industry verticals
Ability to establish and maintain strong customer relationships and to influence others to move toward a common vision or goal
Track record of high customer satisfaction with technical aspect of projects or unique deep subject matter expertise in one or more disciplines
Experience consulting with customers to perform an initial assessment, understand and document business objectives, discuss goals and strategize on how the contact center solution can be deployed to address customer needs
Strong interpersonal writing, editing and presentation skills

BONUS EXPERIENCE
Contact center operational experience and understanding of contact center metrics and their analysis to identify improvement opportunity
Previous experience with contact center workforce management, quality management or analytics systems
In-depth knowledge of Telephony fundamentals including Carrier features, SIP, WebRTC
InContact/CXOne experience, providing technical configuration for IVR specific to CXone Studio programming

Contains bad word ""Project Manager"". Skipping this job!
",Skipped,Not Available
4271843195,https://www.linkedin.com/jobs/view/4271843195,Pending,2025-07-25 01:36:42.202188,2025-07-25 10:36:42.440211,Found a Bad Word in About Job,"
About the job
Our Mission

At Palo Alto Networks® everything starts and ends with our mission:

Being the cybersecurity partner of choice, protecting our digital way of life.

Our vision is a world where each day is safer and more secure than the one before. We are a company built on the foundation of challenging and disrupting the way things are done, and we’re looking for innovators who are as committed to shaping the future of cybersecurity as we are.

Who We Are

We take our mission of protecting the digital way of life seriously. We are relentless in protecting our customers and we believe that the unique ideas of every member of our team contributes to our collective success. Our values were crowdsourced by employees and are brought to life through each of us everyday - from disruptive innovation and collaboration, to execution. From showing up for each other with integrity to creating an environment where we all feel included.

As a member of our team, you will be shaping the future of cybersecurity. We work fast, value ongoing learning, and we respect each employee as a unique individual. Knowing we all have different needs, our development and personal wellbeing programs are designed to give you choice in how you are supported. This includes our FLEXBenefits wellbeing spending account with over 1,000 eligible items selected by employees, our mental and financial health resources, and our personalized learning opportunities - just to name a few!

At Palo Alto Networks, we believe in the power of collaboration and value in-person interactions. This is why our employees generally work full time from our office with flexibility offered where needed. This setup fosters casual conversations, problem-solving, and trusted relationships. Our goal is to create an environment where we all win with precision.



Job Description

Your Career

We are looking for a motivated Senior SRE Engineer to join the Cortex Devops Production group based at our India development ( IDC ) center. In this position you will be working side by side with Cortex Cyber Security Research group. You will be responsible for planning, executing, and reporting on various infrastructure and code projects for Cortex Cyber Security Research Group. Additionally, you will manage and execute high-pressure production maintenance tasks and address related issues.

More information about the Cortex product can be found here

Your Impact

You will take full end-to-end responsibility for the production environment of our SaaS product deployed on GCP
You will build tools for the automatic remediation of known issues
You will develop Infrastructure-as-code which will be used to orchestrate production and dev environments
You will design, build, maintain, and scale production services with thousands of Kubernetes clusters
You will secure the production environments and add in new security tools and features both internal Palo Alto Networks and other market-leading technologies
You will work closely with development teams to design and enhance software architecture to improve scalability, service reliability, cost, and performance
You will build CI pipelines and automation processes
Participate in the on-call rotation supporting the applications and infrastructure
You will research cutting-edge technologies and deploy them to production

Qualifications

Your Experience 

10+ years as DevOps/ SRE Engineer (or equal role) with a passion for technology and strong motivation and responsibility for high reliability and service level
Release automation in the past via GitLab . ArgoCD etc. 
Proficiency with code language (Python / Go - preferred)
High proficiency with Linux
GCP Proficiency in the cloud 
Proficiency with Terraform and HashiCorp tools
HPA via Karpenter / Keda with custom metrics. 
High proficiency with virtualized and containerized environments (Kubernetes and Docker)
Proficiency with CI/CD and Configuration Management (Jenkins preferred)
Proficiency with DB such as Cassandra, ScyllaDB, MemSQL, MySQL - An advantage( Optional ) 
Experience with working with internal and external customers and stakeholders
Managing a high-scale production environment
Excellent communication and interpersonal skills, ability to work and coordinate between multiple teams
Ability to grasp new technologies quickly and prioritize and multitask on multiple responsibilities

Additional Information

The Team

To stay ahead of the curve, it’s critical to know where the curve is, and how to anticipate the changes we’re facing. For the fastest-growing cybersecurity company, the curve is the evolution of cyberattacks and access technology and the products and services that dedicatedly address them. Our engineering team is at the core of our products – connected directly to the mission of preventing cyberattacks and enabling secure access to all on-prem and cloud applications. They are constantly innovating – challenging the way we, and the industry, think about Access and security. These engineers aren’t shy about building products to solve problems no one has pursued before. They define the industry, instead of waiting for directions. We need individuals who feel comfortable in ambiguity, excited by the prospect of challenge, and empowered by the unknown risks facing our everyday lives that are only enabled by a secure digital environment.

Our engineering team is provided with an unrivaled chance to create the products and practices that will support our company growth over the next decade, defining the cybersecurity industry as we know it. If you see the potential of how incredible people and products can transform a business, this is the team for you. If the prospect of affecting tens of millions of people, enabling them to work remotely securely and easily in ways never done before, thrill you - you belong with us.

Our Commitment



We’re problem solvers that take risks and challenge cybersecurity’s status quo. It’s simple: we can’t accomplish our mission without diverse teams innovating, together.

We are committed to providing reasonable accommodations for all qualified individuals with a disability. If you require assistance or accommodation due to a disability or special need, please contact <NAME_EMAIL>.

Palo Alto Networks is an equal opportunity employer. We celebrate diversity in our workplace, and all qualified applicants will receive consideration for employment without regard to age, ancestry, color, family or medical care leave, gender identity or expression, genetic information, marital status, medical condition, national origin, physical or mental disability, political affiliation, protected veteran status, race, religion, sex (including pregnancy), sexual orientation, or other legally protected characteristics.

All your information will be kept confidential according to EEO guidelines.

Contains bad word ""Legal"". Skipping this job!
",Skipped,Not Available
**********,https://www.linkedin.com/jobs/view/**********,Pending,2025-07-24 23:36:46.036697,2025-07-25 10:36:46.124010,Found a Bad Word in About Job,"
About the job
Job Position: ServiceNow Developer ITIL/ITSM 
Location: PAN.
Experience: -5+ to 10+ Years
Must have: - ServiceNow ITIL/ITSM Expert

Requirements:
ServiceNow Implementation Specialist (CIS ITSM), Admin (CSA) and Developer (CAD)
Certified on at least 1 ServiceNow Mainline certification e.g. CMDB, Discovery, Event, Workspace etc
Knowledge of ITIL methodologies and processes and modules such as Asset, Incident, Change, Problem, Knowledge, and Service Catalog, aligning IT services with business objectives.
Knowledge of Service Management (ITSM) processes, including Service Level Agreement (SLA) management and workflow monitoring, ensuring business objectives are met efficiently
Experience in Asset management and CMDB Implementation and Data Normalization.
Experience with Core module (Incident, Problem, Service Request, Knowledge, Change, Service Portal) and on Event management and Discovery Modules.
Ability to create and Modify Workflows with experience in agent workspace design and implementation.
Good at creating required information for Reporting, and Dashboards.
Hands on experience with Java Scripting.
Experience in application design, development and deployment with the Service-now platform.
Experience in migrating data from different tools into ServiceNow.

Contains bad word ""10+ years"". Skipping this job!
",Skipped,Not Available
**********,https://www.linkedin.com/jobs/view/**********,Pending,2025-07-25 10:26:03.483504,2025-07-25 10:37:03.599512,Found a Bad Word in About Job,"
About the job
Company Description
 Whitestone Software Solutions, formed in 2019, provides software products and services for Banking, Financial Services, Micro-Finance, and Government verticals globally. The company has earned a unique reputation for its solutions and delivery excellence, serving more than 20 customers in 5 countries across 2 continents. Whitestone also offers application development, maintenance, consulting, and IT outsourcing services.
 Role Description
 This is a full-time hybrid role for a Java DevOps position located in Bengaluru, with some work-from-home flexibility. The Java DevOps professional will be responsible for software development, continuous integration tasks, integrating various systems, managing Kubernetes environments, and handling Linux-based systems. The role involves collaborating with different teams to ensure efficient development and operational workflows.
 Qualifications
  Proficiency in Software Development and Integration
Experience with Continuous Integration and Kubernetes
Strong Linux-based system management skills
Excellent problem-solving and analytical skills
Ability to work independently and as part of a team
Bachelor’s degree in Computer Science, Information Technology, or a related field
Experience in the financial services sector is a plus

Contains bad word ""Finance"". Skipping this job!
",Skipped,Not Available
4271890234,https://www.linkedin.com/jobs/view/4271890234,Pending,2025-07-25 09:53:06.542106,2025-07-25 10:37:06.630420,Found a Bad Word in About Job,"
About the job
Company Description
Chitrayana Private Limited is a content company specializing in social media marketing and website building. We embrace new ideas and possibilities to create engaging content. Our dynamic team is dedicated to producing high-quality content that resonates with diverse audiences.

Role Description
This is a full-time on-site role for a WordPress Developer located in Bengaluru. The WordPress Developer will be responsible for both back-end and front-end development, including creating WordPress themes and plugins. Day-to-day tasks include building responsive websites, designing and implementing new features and functionalities, and ensuring high performance and availability, and managing all technical aspects of the CMS.

Qualifications
Proficiency in Back-End Web Development
Experience in Front-End Development and Responsive Web Design
Skills in Web Design and Web Development
Strong understanding of PHP, HTML, CSS, JavaScript, and jQuery
Familiarity with the Genesis, Divi, and Elementor themes is a plus
Excellent problem-solving and troubleshooting skills
Strong communication and teamwork skills
Bachelor's degree in Computer Science or a related field

Contains bad word ""Marketing"". Skipping this job!
",Skipped,Not Available
4275506837,https://www.linkedin.com/jobs/view/4275506837,Pending,2025-07-25 07:37:12.971133,2025-07-25 10:37:13.111978,Required experience is high,"
About the job
About Us
MyRemoteTeam, Inc is a fast-growing distributed workforce enabler, helping companies scale with top global talent. We empower businesses by providing world-class software engineers, operations support, and infrastructure to help them grow faster and better.

Job Title: Senior Java Spring Boot Developer
Experience: 6+ Years
Location: Mysore/Bangalore

Job Description:
We are seeking an experienced Senior Java Spring Boot Developer with 6+ years of hands-on experience in building scalable, high-performance microservices using Java, Spring Boot, and Spring JPA. The ideal candidate will have strong expertise in designing and developing RESTful APIs, microservices architecture, and cloud-native applications.
As part of our team, you will work on enterprise-grade applications, collaborate with cross-functional teams, and contribute to the full software development lifecycle.

Mandatory Skills:
✔ 6+ years of Java development (Java 8/11/17).
✔ Strong Spring Boot & Spring JPA experience.
✔ Microservices architecture (design, development, deployment).
✔ RESTful API development & integration.
✔ Database expertise (SQL/NoSQL – PostgreSQL, MySQL, MongoDB).
✔ Testing frameworks (JUnit, Mockito).
✔ Agile methodologies & CI/CD pipelines.

Key Responsibilities:

Design & Development:
Develop high-performance, scalable microservices using Spring Boot.
Design and implement RESTful APIs following best practices.
Use Spring JPA/Hibernate for database interactions (SQL/NoSQL).
Implement caching mechanisms (Redis, Ehcache) for performance optimization.

Microservices Architecture:
Build and maintain cloud-native microservices (Docker, Kubernetes).
Integrate with message brokers (Kafka, RabbitMQ) for event-driven systems.
Ensure fault tolerance, resilience, and scalability (Circuit Breaker, Retry Mechanisms).

Database & Performance:
Optimize database queries (PostgreSQL, MySQL, MongoDB).
Implement connection pooling, indexing, and caching strategies.
Monitor and improve application performance (JVM tuning, profiling).

Testing & Quality Assurance:
Write unit & integration tests (JUnit, Mockito, Test Containers).
Follow TDD/BDD practices for robust code quality.
Perform code reviews and ensure adherence to best practices.

DevOps & CI/CD:
Work with Docker, Kubernetes, and cloud platforms (AWS/Azure).
Set up and maintain CI/CD pipelines (Jenkins, GitHub Actions).
Automate deployments and monitoring (Prometheus, Grafana).

Collaboration & Agile:
Work in Agile/Scrum teams with daily standups, sprint planning, and retrospectives.
Collaborate with frontend, QA, and DevOps teams for seamless delivery.

Experience required 6 > Current Experience 5. Skipping this job!
",Skipped,Not Available
4269656770,https://www.linkedin.com/jobs/view/4269656770,Pending,2025-07-24 21:37:16.467510,2025-07-25 10:37:16.596161,Found a Bad Word in About Job,"
About the job
We are hiring for Full Stack Developer | Gurgaon/Bangalore

Role: Software Engineer - Full Stack
Location: Gurgaon/Bangalore (Hybrid)
Experience-1-3 Years

Mandate Skills- Node JS, Angular, SQL, Java & Spring Boot

Job Description: 
As a Full Stack Developer, you will play a pivotal role in developing and maintaining our web applications (Angular, NodeJS) and backend services (Java, Spring Boot). You will work closely with cross-functional teams to ensure the seamless development and integration of front-end and back-end components, delivering exceptional user experiences. The ideal candidate will have a strong foundation in software development, a keen eye for detail, and a passion for keeping up with emerging technologies.

 Responsibilities:
Collaborate with product managers, UI/UX designers, technical leads, and fellow developers to design and implement robust software solutions.
Participating in daily standup, sprint planning, retrospective meetings during project implementation phase.
Develop responsive and user-friendly front-end interfaces using Angular, ensuring optimal performance across various devices and browsers.
Design and implement RESTful APIs and back-end services using Java/SpringBoot to support front-end functionalities.
Write unit, integration, and end-to-end tests to ensure application quality and performance.
Work with databases and data models to ensure efficient data storage, retrieval, and manipulation.

Skills:
Proven experience (min 1+ years) as a Full Stack Developer with hands-on expertise in Angular, NodeJS and Java with Spring Boot.
Familiarity with the Angular framework and design/architectural patterns (e.g. Microservices, Model-View-Controller (MVC) and Entity framework)
Strong understanding of web development fundamentals, including HTML, CSS, and JavaScript.
Proficiency in designing and consuming RESTful APIs.
Solid knowledge of database systems, SQL, and data modeling.
Familiarity with version control systems (e.g., Git) and agile development methodologies.
Strong problem-solving skills and the ability to work effectively in a collaborative team environment.
Familiarity with Docker, Kubernetes,
Exposure to testing frameworks like Jasmine, Karma (Angular), JUnit, Mockito (Java).
Excellent communication skills, both written and verbal.
Experience using IDE tools like Visual Studio Code and Eclipse.
Self-driven, collaborative, and passionate about clean, maintainable code.

Education and Certification
Bachelor’s degree in Computer Science, Engineering, or related field.
Certification in AWS Certified: Developer Associate or equivalent is a plus.

Interested candidates can send their <NAME_EMAIL>

Contains bad word ""Technical Lead"". Skipping this job!
",Skipped,Not Available
4271808059,https://www.linkedin.com/jobs/view/4271808059,Pending,2025-07-24 17:37:19.365133,2025-07-25 10:37:19.505197,Found a Bad Word in About Job,"
About the job
About us:
SuperAGI is pioneering the future of Artificial General Intelligence with groundbreaking research and innovative AI products. Our mission is to transform the future of applications through intelligent, autonomous solutions that drive unparalleled efficiency and growth. We are building a world where AI and human intelligence collaborate seamlessly to achieve extraordinary outcomes. If you are passionate about AI and eager to be part of a team that is shaping the future, SuperAGI is the place for you.

Job Description:
We are looking for an experienced and highly skilled Senior Manual QA to join our quality assurance team (Immediate joiners preferred). The ideal candidate should possess excellent analytical skills, a strong understanding of product perspectives, and a passion for ensuring high-quality software delivery. As a Manual Tester, you will play a crucial role in our product development process, ensuring that our software meets the highest standards of quality and performance.

Key Responsibilities:
Develop, execute, and maintain comprehensive test plans, test cases, and test scripts to ensure thorough testing of our products.
Perform functional, regression, integration, and user acceptance testing to identify and report defects.
Collaborate with developers, product managers, and other stakeholders to understand product requirements and ensure that the testing covers all critical aspects.
Analyse test results, identify issues, and provide clear and concise feedback to the development team.
Ensure timely and accurate reporting of test results and defects using test management tools.
Focus on process-oriented testing while maintaining agility and speed in test execution.
Drive continuous improvement in testing processes and methodologies to enhance product quality.
He/She Should able to debug and understand failure on automation code. Failure debugging skill set will be needed.

Requirements:
Bachelor’s degree in Computer Science, Information Technology, or a related field
2-5 years of experience in manual testing, with a proven track record of delivering high-quality software.
Strong analytical and problem-solving skills, with the ability to think from both a user and product perspective.
Experience in creating detailed, comprehensive, and well-structured test plans and test cases.
Excellent understanding of software development life cycle (SDLC) and testing methodologies.
Hands-on experience with various testing tools and techniques.
Ability to work independently and as part of a team, with a strong sense of ownership and accountability.
Detail-oriented with a focus on delivering high-quality products in a fast-paced environment.
Strong communication skills, both written and verbal.

Preferred Qualifications:
Experience in testing web and mobile applications.
Familiarity with Agile and Scrum methodologies.
Knowledge of automated testing tools is a plus.

Contains bad word ""Product Manager"". Skipping this job!
",Skipped,Not Available
**********,https://www.linkedin.com/jobs/view/**********,Pending,2025-07-24 14:37:23.202479,2025-07-25 10:37:23.391317,Found a Bad Word in About Job,"
About the job
We're Hiring: Frontend Developer (Web & CMS Focus) | Hyderabad | In-Office
A UK-based Digital Marketing Tech company is expanding its Hyderabad team — and we’re looking for a 

Frontend Developer who can build fast, pixel-perfect landing pages, work closely with designers, and deliver user-first, performance-optimized web experiences.

This isn’t just another framework-heavy role.
 We're looking for someone who thrives in content-led development — turning Figma designs into mobile-first, SEO-friendly websites, and enabling marketing teams to go live faster.

What You’ll Be Doing:
Build & maintain high-converting landing pages from scratch.
Translate Figma designs into responsive, pixel-perfect HTML/CSS.
Work on CMSs like WordPress or Contentful to enable dynamic content updates.
Collaborate cross-functionally to deliver fast, accessible, performance-driven pages.
Optimize for Core Web Vitals and Lighthouse performance.
Deploy using CI/CD, version control, and Git workflows.

Must-Have Skills:
HTML5, CSS3 (SCSS), JavaScript (ES6+), TypeScript
Frameworks: Vue.js + Vuex or React + Redux Toolkit
CMS Experience: WordPress, Contentful, or similar
REST & GraphQL API integration
UI Libraries: Tailwind, Bootstrap, Material UI
Git, Figma, Lighthouse, Chrome DevTools

Soft Skills That Matter:
· Strong communication and stakeholder engagement
· Ownership mindset — able to work with minimal supervision
· Attention to detail and design fidelity
· User-centric, collaborative, and accountable

Why This Role is Different:
· You won’t be buried under framework work — you’ll own the frontend of high-impact landing pages.
· You’ll work closely with Ops and Design, not just engineering.
· You’ll directly impact marketing velocity and performance metrics.

Location: Hyderabad (In-office)
 CTC: Up to ₹18 LPA
 Start: Immediate / ASAP

Apply Now
Send your resume to Ms. Vidya
 <EMAIL> | <EMAIL>

Contains bad word ""Marketing"". Skipping this job!
",Skipped,Not Available
**********,https://www.linkedin.com/jobs/view/**********,Pending,2025-07-24 17:37:25.961956,2025-07-25 10:37:26.082505,Found a Bad Word in About Job,"
About the job
Company Description
SECON Private Limited is an ISO 9001:2015, CMMI Level 3, ISO/IEC 27001:2013, and NABL ISO/IEC 17025:2017 Accredited Firm based in Bangalore, India. SECON is a Geospatial and Multi-discipline Engineering Consulting company offering comprehensive solutions in various domains such as Water Resources, Highways & Infrastructure, and geotechnical investigation. Their international experience includes projects in countries like Libya, Afghanistan, Saudi Arabia, Dubai, and with clients from Europe, the US, and Australia. SECON has been recognized for their excellence with awards like Best Services Exporter 2020 and Best Professionally Managed Company.

Role Description
· Undergo 3 – 6 Months intense Training for understanding LiDAR / Photogrammetry, Remote Sensing, Drone data acquisition, Processing for Geospatial Applications etc using modern Tools, Software / Hardware.
· Form core Team as Developers, Team Leaders, Program Managers, Project Managers and Chief Technical Officer (CTO) in the development of Geospatial related software products and Application.
· Engage in Research on any of the R&D initiatives of SECON as directed by R&D Head
· Study and develop innovative solutions and methodology mostly relating to geospatial applications.
· Undertake all tasks allocated by the Project Leader (as per the Project Plan)
· Be an aggressive member in the Project Team

Production and Processing 
· Execution of the works allotted by the Project/ Team Leader like
Planning & Designing of the Software - Deciding the overall scope of the software to be developed and designing the overall software system framework.
Software Development -Coding, testing of the software project.
Software Deployment - Deploying the developed software at the Client’s site.
Automated routine for Feature extraction using LAS file
Software Documentation - Prepare the necessary documentation related to the Software
User Training - Conduct the End User Training at client’s site
· Responsible to finish the work allotted within the scheduled period with a higher quality
· Technical advice to other team members, if necessary
· Track progress of the execution of tasks and report to the project manager on a daily basis
· Making sure that the allotted work is within the schedule as planned
· Maintain a library of all documentation relating to the execution of allocated tasks
· Escalate issues, risks and changes to the Project Manager for resolution
· Study the latest cutting edge technology and its capabilities and applications in the business market of company
· Suggestion to improve the quality of software developments/ activities and performance

Qualifications
· Post Graduation in Geospatial related Domain with consistent Academic Performance > 60%.
. Strong academic foundation in Mathematics and Physics at the higher secondary and undergraduate levels is a must
. Quick in update, keenness to learn to achieve billability within 3 – 6 Months Training.
· Good Team / People Leadership skill to Lead
· Problem solving / Analytic skills, Computer savvy
· Ability to communicate and coordinate effectively.
· Age below 25 years
· Age and qualification relaxable upto 25 years in the case of experienced candidates.

Do not apply if you’re not ready for long-term engagement or travel-based assignments. We’re building a future-ready team — and we need passionate contributors.

Contains bad word ""Team Lead"". Skipping this job!
",Skipped,Not Available
4271593137,https://www.linkedin.com/jobs/view/4271593137,Pending,2025-07-24 16:37:29.633634,2025-07-25 10:37:29.720261,Found a Bad Word in About Job,"
About the job
Qualifications and Skills for DV Positions:
Bachelor's or Masters degree in Computer Science, Electronics Engineering or equivalent practical experience
7/10+ years of hands-on experience in SystemVerilog/UVM methodology and/or C/C++ based verification
7/ 10+ years experience in IP/sub-system and/or SoC level verification based on SystemVerilog UVM/OVM based methodologies
Experience in development of UVM based verification environments from scratch
Experience in architecting and implementing Design Verification infrastructure and executing the full verification cycle7
Experience with verification of ARM/RISC-V based CPU sub-systems or SoCs
Experience with IP or integration verification along with expertise of protocols like AMBA, PCIe, DDR, USB, Ethernet
Experience in EDA tools and scripting (Python, TCL, Perl, Shell) used to build tools and flows for verification environments
Experience with revision control systems like Mercurial(Hg), Git or SVN

Contains bad word ""10+ years"". Skipping this job!
",Skipped,Not Available
4260824994,https://www.linkedin.com/jobs/view/4260824994,Pending,2025-07-24 16:37:35.915067,2025-07-25 10:37:36.038760,Found a Bad Word in About Job,"
About the job
Company Description

Sandisk understands how people and businesses consume data and we relentlessly innovate to deliver solutions that enable today’s needs and tomorrow’s next big ideas. With a rich history of groundbreaking innovations in Flash and advanced memory technologies, our solutions have become the beating heart of the digital world we’re living in and that we have the power to shape.

Sandisk meets people and businesses at the intersection of their aspirations and the moment, enabling them to keep moving and pushing possibility forward. We do this through the balance of our powerhouse manufacturing capabilities and our industry-leading portfolio of products that are recognized globally for innovation, performance and quality.

Sandisk has two facilities recognized by the World Economic Forum as part of the Global Lighthouse Network for advanced 4IR innovations. These facilities were also recognized as Sustainability Lighthouses for breakthroughs in efficient operations. With our global reach, we ensure the global supply chain has access to the Flash memory it needs to keep our world moving forward.

Job Description

We are seeking a visionary Principal Engineer as part of System Validation Infra to join our team in Bengaluru, India. In this pivotal role, you will lead the technical direction of our engineering efforts, drive innovation, and mentor our talented team of engineers. As a Principal Engineer, you will be at the forefront of designing and implementing cutting-edge solutions and monitoring infra tools and systems deployment that shape the future of our products.

Architect and design scalable, high-performance Infra software that meet business requirements and industry standards
Lead the development of technical strategies and roadmaps to guide long-term engineering initiatives
Collaborate with cross-functional teams to gather requirements and translate them into technical specifications
Provide technical leadership and mentorship to engineering teams, fostering a culture of innovation and continuous improvement
Conduct code reviews and provide constructive feedback to ensure code quality and best practices
Evaluate and recommend new technologies, tools, and methodologies to enhance our engineering capabilities
Drive the resolution of complex technical challenges and make critical design decisions
Participate in the full development lifecycle, from concept to deployment till the maintenance
Contribute to the establishment of engineering standards, best practices, and documentation
Represent the engineering team in high-level discussions with stakeholders and external partners

Qualifications

Bachelor's or Master's degree in Computer Science, Engineering, or a related field, or equivalent practical experience
8+ years of software engineering experience, with at least 3 years in a senior or lead engineering role
Thorough understanding of software architecture, design patterns, and system design principles
Proven track record of leading large to mid-scale, complex software projects from conception to successful delivery
Strong programming skills in language: Python Advanced, C++(Optional)
Basic understanding of CI/CD practices and tools, version control systems (e.g., Git), and agile methodologies
Strong understanding of performance optimization, scalability, and reliability in large to mid-scale systems
Excellent problem-solving skills with the ability to analyze complex issues and develop innovative solutions
Outstanding communication and interpersonal skills, with the ability to collaborate effectively across teams and influence technical decisions
Good knowledge of Flash Memory concepts, Protocol knowledge such as UFS or eMMC shall be an added advantage. 
Experience mentoring and developing junior engineers
Familiarity with monitoring, logging, and observability tools and practices
Strong analytical mindset with attention to detail and a data-driven approach to decision-making
Ability to balance technical excellence with pragmatic solutions to meet business objectives

Additional Information

All your information will be kept confidential according to EEO guidelines.

Contains bad word ""Principal Engineer"". Skipping this job!
",Skipped,Not Available
4270223783,https://www.linkedin.com/jobs/view/4270223783,Pending,2025-07-22 10:37:39.053941,2025-07-25 10:37:39.144284,Found a Bad Word in About Job,"
About the job
Job Title: Fullstack Developer

Experience Required: 2–4 years

Location: Bengaluru (Hybrid) (3 days WFO, 2 days WFH)

Employment Type: Full-time

Key Responsibilities

Design, develop, test, and maintain scalable fullstack applications using modern technologies.
Implement responsive and cross-browser compatible UI components with HTML, CSS (SASS/LESS), and JavaScript.
Develop robust backend services and APIs using Node.js, Express.js, JavaScript/TypeScript.
Collaborate with cross-functional teams including designers, product managers, and other developers.
Ensure code quality through best practices, including unit testing and code reviews.
Contribute to architectural decisions and provide innovative solutions to complex challenges.

Must-Have Qualifications

2–3 years of professional experience as a Fullstack Engineer.
Strong proficiency in HTML, CSS (LESS/SASS), and JavaScript with a deep understanding of responsive and cross-browser web design principles.
Hands-on experience with modern front-end frameworks such as React or Angular.
Solid backend development experience in Node.js, Express.js, and either JavaScript or TypeScript.
Strong grasp of software engineering fundamentals, including data structures, algorithms, and problem-solving skills.

Good-to-Have Skills

Experience building applications that integrate AI tools or AI-driven workflows.
Exposure to backend development using Python or Java.
Knowledge of databases, including MongoDB, DynamoDB, MySQL, Redis, ElastiCache, and ElasticSearchDB.
Experience designing and developing RESTful APIs, preferably with metric-driven API Gateway integrations.
Familiarity with AWS services, Kubernetes, microservices, and domain-driven architecture.
Excellent written and verbal communication skills, with the ability to clearly present technical concepts to stakeholders and team members.

Skills: angular,typescript,html,node.js,express.js,javascript,react,css,restful apis,css (sass/less)

Contains bad word ""Product Manager"". Skipping this job!
",Skipped,Not Available
4275450185,https://www.linkedin.com/jobs/view/4275450185,Pending,2025-07-25 01:37:45.546888,2025-07-25 10:37:45.685684,Found a Bad Word in About Job,"
About the job
Our Mission

At Palo Alto Networks® everything starts and ends with our mission:

Being the cybersecurity partner of choice, protecting our digital way of life.

Our vision is a world where each day is safer and more secure than the one before. We are a company built on the foundation of challenging and disrupting the way things are done, and we’re looking for innovators who are as committed to shaping the future of cybersecurity as we are.

Who We Are

We take our mission of protecting the digital way of life seriously. We are relentless in protecting our customers and we believe that the unique ideas of every member of our team contributes to our collective success. Our values were crowdsourced by employees and are brought to life through each of us everyday - from disruptive innovation and collaboration, to execution. From showing up for each other with integrity to creating an environment where we all feel included.

As a member of our team, you will be shaping the future of cybersecurity. We work fast, value ongoing learning, and we respect each employee as a unique individual. Knowing we all have different needs, our development and personal wellbeing programs are designed to give you choice in how you are supported. This includes our FLEXBenefits wellbeing spending account with over 1,000 eligible items selected by employees, our mental and financial health resources, and our personalized learning opportunities - just to name a few!

At Palo Alto Networks, we believe in the power of collaboration and value in-person interactions. This is why our employees generally work full time from our office with flexibility offered where needed. This setup fosters casual conversations, problem-solving, and trusted relationships. Our goal is to create an environment where we all win with precision.



Job Description

Your Career

We are looking for a motivated Senior SRE Engineer to join the Cortex Devops Production group based at our India development ( IDC ) center. In this position you will be working side by side with Cortex Cyber Security Research group. You will be responsible for planning, executing, and reporting on various infrastructure and code projects for Cortex Cyber Security Research Group. Additionally, you will manage and execute high-pressure production maintenance tasks and address related issues.

More information about the Cortex product can be found here

Your Impact

You will take full end-to-end responsibility for the production environment of our SaaS product deployed on GCP
You will build tools for the automatic remediation of known issues
You will develop Infrastructure-as-code which will be used to orchestrate production and dev environments
You will design, build, maintain, and scale production services with thousands of Kubernetes clusters
You will secure the production environments and add in new security tools and features both internal Palo Alto Networks and other market-leading technologies
You will work closely with development teams to design and enhance software architecture to improve scalability, service reliability, cost, and performance
You will build CI pipelines and automation processes
Participate in the on-call rotation supporting the applications and infrastructure
You will research cutting-edge technologies and deploy them to production

Qualifications

Your Experience 

4+ years as DevOps Engineer (or equal role) with a passion for technology and strong motivation and responsibility for high reliability and service level
Proficiency with code language (Python / Go - preferred)
High proficiency with Linux
Proficiency in the cloud (GCP - preferred)
Proficiency with Terraform and HashiCorp tools
High proficiency with virtualized and containerized environments (Kubernetes and Docker)
Proficiency with CI/CD and Configuration Management (Jenkins preferred)
Proficiency with DB such as Cassandra, ScyllaDB, MemSQL, MySQL - An advantage (Optional) 
Experience with working with internal and external customers and stakeholders
Managing a high-scale production environment
Excellent communication and interpersonal skills, ability to work and coordinate between multiple teams
Ability to grasp new technologies quickly and prioritize and multitask on multiple responsibilities

Additional Information

The Team

To stay ahead of the curve, it’s critical to know where the curve is, and how to anticipate the changes we’re facing. For the fastest-growing cybersecurity company, the curve is the evolution of cyberattacks and access technology and the products and services that dedicatedly address them. Our engineering team is at the core of our products – connected directly to the mission of preventing cyberattacks and enabling secure access to all on-prem and cloud applications. They are constantly innovating – challenging the way we, and the industry, think about Access and security. These engineers aren’t shy about building products to solve problems no one has pursued before. They define the industry, instead of waiting for directions. We need individuals who feel comfortable in ambiguity, excited by the prospect of challenge, and empowered by the unknown risks facing our everyday lives that are only enabled by a secure digital environment.

Our engineering team is provided with an unrivaled chance to create the products and practices that will support our company growth over the next decade, defining the cybersecurity industry as we know it. If you see the potential of how incredible people and products can transform a business, this is the team for you. If the prospect of affecting tens of millions of people, enabling them to work remotely securely and easily in ways never done before, thrill you - you belong with us

Our Commitment



We’re problem solvers that take risks and challenge cybersecurity’s status quo. It’s simple: we can’t accomplish our mission without diverse teams innovating, together.

We are committed to providing reasonable accommodations for all qualified individuals with a disability. If you require assistance or accommodation due to a disability or special need, please contact <NAME_EMAIL>.

Palo Alto Networks is an equal opportunity employer. We celebrate diversity in our workplace, and all qualified applicants will receive consideration for employment without regard to age, ancestry, color, family or medical care leave, gender identity or expression, genetic information, marital status, medical condition, national origin, physical or mental disability, political affiliation, protected veteran status, race, religion, sex (including pregnancy), sexual orientation, or other legally protected characteristics.

All your information will be kept confidential according to EEO guidelines.


Is role eligible for Immigration Sponsorship? No. Please note that we will not sponsor applicants for work visas for this position.

Contains bad word ""Legal"". Skipping this job!
",Skipped,Not Available
**********,https://www.linkedin.com/jobs/view/**********,Pending,2025-07-24 15:37:51.558008,2025-07-25 10:37:51.689779,Found a Bad Word in About Job,"
About the job
Company Description

The future. It’s on you. You & Western Digital.

We’ve been storing the world’s data for more than 50 years. Once, it was the most important thing we could do for data. Now we’re helping the world capture, preserve, access and transform data in a way only we can.

The most game-changing companies, consumers, professionals, and governments come to us for the technologies and solutions they need to capture, preserve, access, and transform their data.

But we can’t do it alone. Today’s exceptional data challenges require your exceptional skills. It’s You & Us. Together, we’re the next big thing in data. Western Digital® data-centric solutions are found under the G-Technology™, HGST, SanDisk®, Tegile™, Upthere™, and WD® brands.

Western Digital is an equal opportunity employer. Western Digital does not discriminate on the basis of race, color, ancestry, religion (including religious dress and grooming standards), sex (including pregnancy, childbirth or related medical conditions, breastfeeding or related medical conditions), gender (including a person’s gender identity, gender expression, and gender-related appearance and behavior, whether or not stereotypically associated with the person’s assigned sex at birth), age, national origin, sexual orientation, medical condition, marital status (including domestic partnership status), physical disability, mental disability, medical condition, genetic information, protected medical and family care leave, Civil Air Patrol status, military and veteran status, or other legally protected characteristics. We also prohibit harassment of any individual on any of the characteristics listed above. Our non-discrimination policy applies to all aspects of employment. We comply with the laws and regulations set forth in the ""Equal Employment Opportunity is the Law"" poster.

Federal and state laws require employers to provide reasonable accommodation to qualified individuals with disabilities. Please tell us if you require a reasonable accommodation to apply for a job or to perform your job. Examples of reasonable accommodation include making a change to the application process or work procedures, providing documents in an alternate format, using a sign language interpreter, or using specialized equipment. If you need any accommodation or assistance with our career site, please contact <NAME_EMAIL>.

Western Digital participates in the E-Verify program in the US. For more information click here. Este empleador participa in E-Verify.

Job Description

ESSENTIAL DUTIES AND RESPONSIBILITIES:

Need experienced Validation engineers who understand embedded NAND system design and firmware algorithms in order to create a test validation plans and implement them in modern object oriented languages.

Work closely with the system architects and the firmware team to develop Validation plans, test bench and test cases
Develop an overall validation strategy including defining validation infrastructure and validation methodology
Debug the firmware and expose design issues
Define and design functional tests required to meet customer needs
Review SanDisk UFS/eMMC embedded NAND validation requirements and influence future SanDisk product design for debug and test
Work with customers to understand field bugs and to enhance the validation coverage
Interface with all key stakeholders to ensure product validation meets customer expectations and needs

Qualifications

REQUIRED:

Bachelor or Master in Computer Science, Computer/Electronics Engineering or equivalent required.
Minimum 5+ years in storage related segment
Capable of quickly learning hardware, systems, tools, and methodologies. 

Technical And Analytical Skills Required

In depth understanding of firmware algorithms used in any NAND Flash based storage devices (SSD, eMMC, UFS, SD, USB Flash drives) or other storage devices
Knowledge in any Host protocols like as UFS, eMMC, SCSI, SATA, PCIe, NVMe is an added advantage
Experienced and familiar with firmware development, Integration and validation
Knowledgeable on product and quality standards and relevant host protocols, in particular eMMC and UFS.
Experience in the area of Grey box or White box.
Test coverage methods for real-time embedded systems, especially storage systems and/or NAND storage
Able to methodically root cause complex failure mechanism
Strong programming knowledge & debugging skills in Python, C and C++ , Shell.
Soft skills, Excellent written and verbal skills, be a Team player
Able to develop key relationships
Able to elect requirements from all stakeholders
Be able to work in pressure for quick resolution and delivery.
Knowledge on Mobile/Compute/Industrial OS and Filesystem
Experience in the area of digital and analog design

Additional Information

Because Western Digital thrives on the power of diversity and is committed to an inclusive environment where every individual can thrive through a sense of belonging, respect, and contribution, we are committed to giving every qualified applicant and employee an equal opportunity. Western Digital does not discriminate against any applicant or employee based on their protected class status and complies with all federal and state laws against discrimination, harassment, and retaliation, as well as the laws and regulations set forth in the ""Equal Employment Opportunity is the Law"" poster.

Part of creating a diverse and inclusive workplace includes ensuring that all qualified applicants and employees are provided equal consideration for any available opportunity. Western Digital is committed to offering opportunities to applicants with a disability. If you need a reasonable accommodation, email <NAME_EMAIL> your email, please include a description of the specific accommodation you are requesting as well as the job title and requisition number of the position for which you are applying.

Contains bad word ""Legal"". Skipping this job!
",Skipped,Not Available
4271538684,https://www.linkedin.com/jobs/view/4271538684,Pending,2025-07-24 10:38:17.013175,2025-07-25 10:38:17.197794,Found a Bad Word in About Job,"
About the job
Trelleborg is a world leader in engineered polymer solutions for almost every industry on the planet. And we are where we are because our talents brought us here. By specializing in the polymer engineering that makes innovation and application possible, Trelleborg works closely with leading industry brands to accelerate their performance, drive their business forward—and along the way, shape the industry and progress that will benefit humankind in the exciting years ahead. Our people are Shaping Industry from the Inside. Why don´t you join us? 
Are you a talent looking to build business skills, gain experience, and take on exciting challenges? Grow your career with Trelleborg and start shaping the industry from the inside.

We are looking for highly skilled and experienced Senior Software Engineer - Development to join our IT Innovation Campus at Bangalore location. The position will assist for architecting and developing high-performance, scalable software solutions, mentoring junior engineers, and collaborating across teams to build impactful products.

Your Responsibilities:
Create scalable and maintainable architectures to solve business problems
Design, develop, test, and maintain software applications, services, and
Mentor and guide junior engineers, providing technical support and sharing best
Lead and participate in writing clean, maintainable, and high-quality Ensure code is well- documented and adheres to industry best practices.
Conduct regular code reviews to ensure code quality and to foster a culture of Ensure that automated tests are in place and that systems are thoroughly tested.
Work closely with cross-functional teams, including product managers, designers, and QA engineers, to gather requirements and deliver robust solutions.
Investigate, analyze, and resolve complex technical Implement performance tuning and optimization strategies as needed.
Contribute to the product vision and continuously improve existing systems, applications, and services.

What You'll Bring:
Bachelor’s or master’s
5+ years of professional experience in software engineering, with at least 2-3 years in a senior or lead role.
Strong proficiency skills required - HTML5/CSS3, JavaScript, TypeScript, React JS, Java/Jee, Spring Boot, Spring Data JPA, Relational Database (Oracle/MySQL DB), Microservices architecture (e.g., Java, Python, JavaScript) with a deep understanding of software development fundamentals.
Strong experience using Git, Maven or other version control Systems
Strong analytical skills and a demonstrative ability to solve complex technical problems.
Excellent written and verbal communication skills, with the ability to explain complex technical concepts to non-technical stakeholders.
Solid experience in designing complex, scalable systems and web applications, including microservices and cloud-native architectures. Familiarity with Agile and Scrum development
Experience with relational (SQL) or NoSQL databases (e.g., MySQL, PostgreSQL, MongoDB).
Familiarity with CI/CD pipelines, automation, and tools like Jenkins, GitLab, or
Excellent analytical and troubleshooting skills with a passion for solving complex challenges.
Expertise in modern web UI frameworks/libraries (e.g., React, Angular, Vue.js) and full-stack development.

Why Work with Us?
At Trelleborg, your career progression and personal development are of utmost importance to us. In our vibrant and dynamic work setting, every contribution you make is recognized and your professional aspirations are actively fostered. Committed to innovation, excellence, and ecological sustainability, we ensure that your efforts contribute not just to our organizational achievements but also to global technological and data management advancements. Seize this chance to make a meaningful difference at Trelleborg, where you face stimulating challenges, your growth is certain, and your career can flourish.

Application Process
Trelleborg is an equal opportunity employer! We celebrate diversity and are committed to creating an inclusive environment for all employees. We will ensure that individuals with disabilities are provided reasonable accommodation to participate in the job application or interview process, to perform crucial job functions, and to receive other benefits and privileges of employment. Please contact us to request accommodation.

At Trelleborg, our people are #ShapingIndustryfromtheInside#

Feel free to contact our HR Team for any questions:

Ashwini @ <EMAIL>

Contains bad word ""Senior Software Engineer"". Skipping this job!
",Skipped,Not Available
4271336979,https://www.linkedin.com/jobs/view/4271336979,Pending,2025-07-24 10:38:23.732901,2025-07-25 10:38:23.817157,Found a Bad Word in About Job,"
About the job
About the role:
We’re hiring a founding engineer to join us in shaping the next generation of consumer app infrastructure. This will be an in-person role in Bangalore. We’re flexible on compensation and are willing to pay top of band for the right person. We’re looking for someone who’s hungry to grow and be as AI-native as possible.

What are we looking for:
Design and implement scalable backend services using Java.
Participate in architecture and design discussions with a focus on scalability and performance.
Write clean, maintainable code with unit and integration tests.
Collaborate with cross-functional teams to deliver high-quality features.
Take ownership of modules and drive them end-to-end.

Why should you join us:
Experience the chaos of 0-1 startup journey along with other smart and hungry people
Build cutting edge tech to help scale the next generation of consumer apps
Court-side view to applied AI; You will have free reign to apply AI wherever you see fit, and fund any learning / SaaS expenses that you want to experiment with
Broaden your skill-set - totally open to structuring a path to whatever role you’re interested in over time (sales, ML, product, design, data etc.)

Contains bad word ""Sales"". Skipping this job!
",Skipped,Not Available
**********,https://www.linkedin.com/jobs/view/**********,Pending,2025-07-19 10:38:28.554374,2025-07-25 10:38:28.621642,Found a Bad Word in About Job,"
About the job
Optimum Data Analytics is a strategic technology partner delivering reliable turnkey AI solutions. Our streamlined approach to development ensures high-quality results and client satisfaction. We bring experience and clarity to organizations, powering every human decision with analytics & AI.

Our team consists of statisticians, computer science engineers, data scientists, and product managers. With expertise, flexibility, and cultural alignment, we understand the business, analytics, and data management imperatives of your organization. Our goal is to change how AI/ML is approached in the service sector and deliver outcomes that matter.

We provide best-in-class services that increase profit for businesses and deliver improved value for customers, helping businesses grow, transform, and achieve their objectives

Job Title : Full Stack Developer – React.js, Python, SQL & NoSQL

Location : Bangalore

Experience : 4-7 years

Required Skills

Frontend React.js with Shadcn, Redux, HTML5, CSS3, tailwind CSS

Backend Python, Flask / FastAPI

Databases SQL Server, MongoDB

API Integration REST APIs

Version Control Git, GitHub

Testing Jest, PyTest Postman, Swagger

Deployment (Plus) Docker, CI/CD, Linux scripting

Skills: ci/cd,tailwind css,mongodb,sql,html5,fast api,shadcn,fastapi,python,postman,redux,jest,git,linux scripting,rest apis,docker,pytest,swagger,backend python,css3,react.js,github,sql server,flask

Contains bad word ""Product Manager"". Skipping this job!
",Skipped,Not Available
4275513414,https://www.linkedin.com/jobs/view/4275513414,Pending,2025-07-25 07:38:35.713798,2025-07-25 10:38:35.859249,Found a Bad Word in About Job,"
About the job
About Us
MyRemoteTeam, Inc is a fast-growing distributed workforce enabler, helping companies scale with top global talent. We empower businesses by providing world-class software engineers, operations support, and infrastructure to help them grow faster and better.

Job Title: Full Stack Developer – Java + React
Experience: 5+ Years

Job Summary:
We are looking for a talented and experienced Full Stack Developer (Java + React) with a strong background in building scalable, high-quality, and high-performance web applications. The ideal candidate will have hands-on experience in Java, Spring Boot, Microservices architecture, and React.js.

Key Responsibilities:
Design, develop, and maintain scalable backend services using Java, Spring Boot, and Microservices.
Develop intuitive and responsive frontend interfaces using React.js.
Collaborate with product managers, UI/UX designers, and other developers to understand requirements and translate them into technical solutions.
Ensure code quality through unit testing, code reviews, and best practices.
Integrate APIs and work with third-party services as needed.
Optimize application performance and scalability.
Participate in Agile/Scrum development cycles, including sprint planning and daily stand-ups.
Troubleshoot and resolve production issues in a timely manner.

Required Skills:
5+ years of experience in Java development
Strong experience with Spring Boot and building RESTful APIs
Solid understanding of Microservices architecture
2+ years of hands-on experience with React.js, JavaScript, HTML5, and CSS3
Experience working with databases like MySQL, PostgreSQL, or MongoDB
Familiarity with version control systems (Git)
Understanding of CI/CD pipelines and deployment processes
Good problem-solving skills and attention to detail

Contains bad word ""Product Manager"". Skipping this job!
",Skipped,Not Available
4275394243,https://www.linkedin.com/jobs/view/4275394243,schin_m resume.pdf,2025-07-24 20:38:36.190391,2025-07-25 10:38:43.400157,Problem in Easy Applying,"Message: stale element reference: stale element not found in the current frame
  (Session info: chrome=138.0.7204.169); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#staleelementreferenceexception
Stacktrace:
	GetHandleVerifier [0x0x7ff7ae4ee935+77845]
	GetHandleVerifier [0x0x7ff7ae4ee990+77936]
	(No symbol) [0x0x7ff7ae2a9cda]
	(No symbol) [0x0x7ff7ae2b1679]
	(No symbol) [0x0x7ff7ae2b471c]
	(No symbol) [0x0x7ff7ae2b47ef]
	(No symbol) [0x0x7ff7ae30858b]
	(No symbol) [0x0x7ff7ae305ab1]
	(No symbol) [0x0x7ff7ae303081]
	(No symbol) [0x0x7ff7ae301f81]
	(No symbol) [0x0x7ff7ae2f36c8]
	(No symbol) [0x0x7ff7ae3288ca]
	(No symbol) [0x0x7ff7ae2f2f76]
	(No symbol) [0x0x7ff7ae328ae0]
	(No symbol) [0x0x7ff7ae350b07]
	(No symbol) [0x0x7ff7ae3286a3]
	(No symbol) [0x0x7ff7ae2f1791]
	(No symbol) [0x0x7ff7ae2f2523]
	GetHandleVerifier [0x0x7ff7ae7c684d+3059501]
	GetHandleVerifier [0x0x7ff7ae7c0c0d+3035885]
	GetHandleVerifier [0x0x7ff7ae7e0400+3164896]
	GetHandleVerifier [0x0x7ff7ae508c3e+185118]
	GetHandleVerifier [0x0x7ff7ae51054f+216111]
	GetHandleVerifier [0x0x7ff7ae4f72e4+113092]
	GetHandleVerifier [0x0x7ff7ae4f7499+113529]
	GetHandleVerifier [0x0x7ff7ae4de298+10616]
	BaseThreadInitThunk [0x0x7ff99ab1e8d7+23]
	RtlUserThreadStart [0x0x7ff99c43c34c+44]
",Easy Applied,Not Available
4271329212,https://www.linkedin.com/jobs/view/4271329212,Pending,2025-07-24 10:38:49.363530,2025-07-25 10:38:49.486620,Required experience is high,"
About the job
Job Title: Full-Stack Developer
Experience: 6+ years
Top Skills: Java, Spring, React, SQL, TypeScript
Work Mode: Hybrid - 3 days from the office
Work Location: Marathahalli, Bangalore.

Job Summary:
We âre looking for a Full-Stack Developer with solid experience in Java, React, and SQL. You âll be part of a collaborative team, building and enhancing responsive web applications using modern technologies.
Key Responsibilities:
Build full-stack applications using Java, React, and GraphQL.
Create responsive web interfaces that interact with backend APIs.
Communicate clearly with team members across functions.
Take part in Agile practices like sprint planning, reviews, and retrospectives.
Contribute to improving the development process and sharing best practices.

Must-Have Skills:
Strong Java and Spring framework knowledge.
Proficiency in React and TypeScript.
Solid SQL and API integration experience.
Understanding of GraphQL and frontend technologies like JavaScript.
Good communication and teamwork abilities.

Good to Have:
Exposure to Magnolia CMS or Next.js.
Experience working with open-source projects.
Familiarity with Agile development processes.

Experience required 6 > Current Experience 5. Skipping this job!
",Skipped,Not Available
4271807724,https://www.linkedin.com/jobs/view/4271807724,Pending,2025-07-24 18:39:07.085105,2025-07-25 10:39:07.195771,Found a Bad Word in About Job,"
About the job
Job Description -

Strong in Teradata ETL architecture & SQL development. · Independently analyse, develop, test & execute assigned modules with ownership attitude.
The role focuses on developing and maintaining Teradata-based data warehousing solutions. Responsibilities include writing SQL queries, designing database structures, and optimizing performance for large datasets.
Continuously learning about the Teradata platform and related technologies.
Working with a team of developers, business analysts, and other stakeholders to deliver data warehousing solutions.
Participating in testing activities, including unit testing, system integration testing (SIT), and regression testing.
Working with data models, including Teradata FSLDM (Financial Services Logical Data Model), and creating mapping documents.

Contains bad word ""Business Analyst"". Skipping this job!
",Skipped,Not Available
4271301603,https://www.linkedin.com/jobs/view/4271301603,Pending,2025-07-24 10:39:24.560846,2025-07-25 10:39:24.632848,Required experience is high,"
About the job
We are actively looking for DevOps Engineer.
About the Role
The Software Engineer will be responsible for developing and maintaining software solutions that meet the needs of our clients.
Qualification:

Experience: 10 years to 18 years
Notice Period: Immediate Joiner
Location: Bangalore

Required Skills
AWS
Preferred Skills
Experience with additional programming languages
Familiarity with cloud services

Experience required 10 > Current Experience 5. Skipping this job!
",Skipped,Not Available
4273995470,https://www.linkedin.com/jobs/view/4273995470,Pending,2025-07-24 10:39:30.488483,2025-07-25 10:39:30.746177,Found a Bad Word in About Job,"
About the job
Hi,
Please find JD attached with company description for your reference. If you're interested, please fill the below form

https://forms.gle/pYe6pBEMDKGS2LibA
I'll Connect with you soon, once I receive interview slots.

Email - <EMAIL>

Position: Senior Frontend Developer
Experience: 5 to 7 Years
Location: Bangalore (Hybrid/Onsite depending on client requirement)

Key Skills
• Primary: React.js, Redux, Micro Frontend Architecture, JavaScript (ES6+)
• Secondary: Next.js, Tailwind CSS

Job Description

We are seeking a skilled and experienced Senior Frontend Developer with expertise in building scalable, high-performance web applications using React.js and Redux within a Microfrontend architecture. The ideal candidate should be passionate about clean code, performance optimization, and delivering seamless user experiences.

Responsibilities
• Develop and maintain complex front-end applications using React.js and Redux.
• Architect and implement Microfrontend solutions for large-scale web platforms.
• Optimize application for maximum speed, scalability, and responsiveness.
• Integrate RESTful APIs and collaborate closely with backend teams.
• Apply best practices in coding, design, and architecture to ensure code quality.
• Enhance UI/UX using Tailwind CSS and modern design principles.
• Collaborate with cross-functional teams including Product, Design, and QA.
• Mentor junior developers and contribute to code reviews.
• Stay updated with the latest trends in front-end technologies.

Nice to Have
• Experience with Next.js for SSR (Server-Side Rendering) and static site generation.
• Familiarity with CI/CD pipelines for front-end deployment.
• Prior experience working in Agile/Scrum teams.

Qualifications
• 5-7 years of hands-on experience in React.js, Redux, JavaScript.
• Strong understanding of Micro frontend architecture and implementation strategies.
• Proficiency in Next.js and Tailwind CSS is preferred.
• Good problem-solving skills and attention to detail.
• Excellent communication and collaboration skills.
Company Introduction

Bounteous x Accolite makes the future faster for the world’s most ambitious brands. Our services span Strategy, Analytics, Digital Engineering, Cloud, Data & AI, Experience Design, and Marketing. We are guided by Co-Innovation, our proven methodology of collaborative partnership. Bounteous x Accolite brings together 5,000+ employees spanning North America, APAC, and EMEA, and partnerships with leading technology providers. Through advanced digital engineering, technology solutions, and data-driven digital experiences, we create exceptional and efficient business impact and help our clients win.
For more information visit: www.accolite.com

Accolite Digital India Pvt Ltd, is focused on serving Fortune 500 Customers in Healthcare, Banking and Financial Services, Telecommunications and Automotive Verticals. The company is focused on producing the best technical talent in these Verticals and solving most complex technical and business problems. Accolite is a cutting edge information technology services and product development company headquartered in Dallas, Texas, with four development centres in Bangalore, Delhi, Mumbai, Hyderabad and Coimbatore, India.

1. A young IT Services company headquartered in Texas with business Operations in the US, Canada, UK, Middle East, and India. (About 15 years old)
2. In India we have offices in Gurgaon, Hyderabad, Mumbai, Chennai, and Bangalore. In 2021 we have expansion plans to 3 other European and Latin American countries
3. Accolite has a team of 5000+ headcount across its locations.
4. We have recently entered into a Strategic Partnership with New Mountain Capital, a leading growth-oriented investment firm with over $30 billion in assets under management who also own companies like eMids.
5. We have 12+ Fortune 500 clients including some of the biggest Banks and Telecom companies in the world.
6. Accolite had been growing at a 30% + growth rate in at least 3 years and targets to become a half Billion Dollar company in the next 4 years.
7. Open door flexible performance-driven work culture

Accolite Digital is a leading digital transformation services provider that delivers design-led, complex digital transformation initiatives to Fortune 500 clients. Our differentiated services span digital product engineering, cloud and DevOps, data and AI, customer experience, cyber security, and design services. Accolite Digital provides these services to the banking and financial services, insurance, technology, media and telecom, healthcare, and logistics industries. With more than 5000 professionals globally, Accolite has presence across the United States, Canada, Mexico, Europe, and India, with digital labs in Bangalore, Hyderabad, Gurugram, and Chennai.

For more information visit: www.accolite.com

About Bounteous
Founded in 2003 in Chicago, Bounteous is a leading digital experience consultancy that co-innovates with the world’s most ambitious brands to create transformative digital experiences. With services in Strategy, Experience Design, Technology, Analytics and Insight, and Marketing, Bounteous elevates brand experiences and drives superior client outcomes. For more information, please visit www.bounteous.com.
For more information about Co-Innovation, download the Co-Innovation Manifesto at co-innovation.com.
For the most up-to-date news, follow Bounteous on Twitter, LinkedIn, Facebook, and Instagram.

Contains bad word ""Marketing"". Skipping this job!
",Skipped,Not Available
**********,https://www.linkedin.com/jobs/view/**********,Pending,2025-07-22 10:39:32.399228,2025-07-25 10:39:32.665117,Found a Bad Word in About Job,"
About the job
Position - Frontend - Mobile RN Developer

Experience - 5+ Years

Location - Multiple locations across India. Here are some of the key locations:
Bangalore
Bhubaneswar
Chennai
Coimbatore
Gandhinagar
Gurugram
Hyderabad
Kolkata
Mumbai
Noida
Pune
Salem
Tiruchirappalli

Must Have Skills - Native module , Axios , Custom hooks , Husky , SonarQube , Redux , info.plist , manisfest.xml 

Must Have:
Expertise in strategizing and developing mobile applications for both iOS and Android platforms using React Native.
5+ years of hands-on experience in building and designing mobile applications with React Native - TypeScript.
Strong proficiency with Redux-Saga for managing application state and handling side effects.
Solid knowledge of JavaScript, TypeScript, Swift, and Kotlin for cross-platform and native mobile development.
Experience integrating third-party libraries into React Native apps, such as Firebase, Sentry, and others for push notifications, analytics, and crash reporting.
Ability to build and design reusable NPM packages for multiple projects, promoting efficient code sharing.
Proven experience developing custom native modules for at least one platform (iOS with Swift/Obj-C or Androidwith Java/Kotlin).
Proficient in creating React Native components that are efficient, maintainable, and easy to test.
Strong knowledge of unit testing and writing test cases using Jest to ensure high code quality.
Version control using Git to maintain a clean and organized codebase.
Experience working with design systems such as Atomic Design or Fabric to maintain consistency across applications.
Familiarity with Figma to translate design specs into well-crafted, functional mobile interfaces.
Comfortable using collaboration tools like JIRA, Confluence, and other project management software to track progress and communicate effectively within teams.

About CLPS RiDiK
RiDiK is a global technology solutions provider and a subsidiary of CLPS Incorporation (NASDAQ: CLPS), delivering cutting-edge end-to-end services across banking, wealth management, and e-commerce. With deep expertise in AI, cloud, big data, and blockchain, we support clients across Asia, North America, and the Middle East in driving digital transformation and achieving sustainable growth. Operating from regional hubs in 10 countries and backed by a global delivery network, we combine local insight with technical excellence to deliver real, measurable impact. Join RiDiK and be part of an innovative, fast-growing team shaping the future of technology across industries.

Contains bad word ""Blockchain"". Skipping this job!
",Skipped,Not Available
**********,https://www.linkedin.com/jobs/view/**********,Pending,2025-07-24 10:39:36.363205,2025-07-25 10:39:36.490012,Found a Bad Word in About Job,"
About the job
Now Hiring: Mobile App Developer (iOS + Xamarin/.NET MAUI)

27521

📍 Location: Bangalore (South India candidates preferred)💰 CTC Range: ₹15 – 28 LPA📌 Domain: Information Technology💼 Experience: 5–7 Years📆 Interview Process: L1 → L2 → Executive Leadership (EL)

💼 Role Overview

looking for a skilled Mobile App Developer with strong iOS development skills and hands-on experience in Xamarin and/or .NET MAUI. You’ll be part of a high-impact team building cross-platform mobile applications focused on performance, usability, and scalability.

✅ Must-Have Skills

Strong iOS mobile development experience using Swift, Objective-C, or C# via Xamarin/.NET MAUI 
Proficiency in Xamarin.Forms, Xamarin.Native, or .NET MAUI 
Solid understanding of mobile app architecture and design patterns (MVVM/MVC/Clean Architecture) 
Experience in RESTful API integration, JSON parsing, async programming 
Hands-on experience with app lifecycle management, offline storage, push notifications, and mobile app security 
Familiarity with publishing apps to the Apple App Store 
Strong collaboration skills – work closely with product managers, designers, and backend engineers 

🌟 Nice To Have

Experience in Android development 
Familiarity with Google Play Store publishing 
Exposure to Firebase, analytics tools, or other 3rd-party services 
Knowledge of Agile/Scrum methodologies 

Skills: objective-c,app,architecture,offline storage,mobile app architecture,restful api integration,apple app store publishing,mvvm,async programming,ios,xamarin.native,json parsing,mvc,collaboration skills,ios development,swift,api,c#,mobile app security,clean architecture,app lifecycle management,xamarin.forms,push notifications,publishing,.net maui,xamarin

Contains bad word ""Product Manager"". Skipping this job!
",Skipped,Not Available
4270976951,https://www.linkedin.com/jobs/view/4270976951,Pending,2025-07-24 10:40:06.489931,2025-07-25 10:40:06.748165,Found a Bad Word in About Job,"
About the job
About us:
Pixis is a US-based codeless technology company that develops accessible AI to empower brands to scale their performance marketing efforts and augment their decision-making seamlessly. Since its inception, Pixis has been on a mission to develop powerful AI infrastructure that equips marketers across countries with robust plug-and-play AI products, 200+ proprietary, self-evolving AI models without having to write a single line of code. 

The company has raised a total funding of $209M across Series A, B, C and C1, and is backed by recognized investors including SoftBank Vision Fund 2, Touring Capital, Grupo Carso, General Atlantic, Celesta Capital and Chiratae Ventures. 

Our customer base includes global brands such as DHL Express, Joe & The Juice, Claroshop, Allbirds, L’Oreal, HDFC Bank, Skoda, Swiggy, Clar and SmartAsset, to name a few. Today Pixis’ talented and diverse team of 300+ spread across over 14 geographies is focused on building incredibly transformative AI products to help customers get the most out of their marketing and demand generation efforts. 

Get ready to embark on an AI venture at https://pixis.ai/

Why Pixis? 
We at Pixis believe that nothing is impossible, when you fail fast you learn faster, zero hierarchy, put the team above everything else, get constructive feedback that helps you build better products, and disagree if you disbelieve. These values guide us in everything we do, and is reflected in our employees and the products we build together. 
Our commitment to fostering an exceptional workplace has been recognized with the prestigious People Workplace Awards 2024 by the HR Association of India, a testament to our dedication to creating an outstanding environment for our employees.
Join Pixis and be a part of a team where innovation knows no bounds, every idea counts, and together, we shape the future of technology.

Key Responsibilities:
As a Generative AI Engineer at Pixis, you will be at the forefront of designing and implementing next-generation AI models that drive real business impact. Your contributions will directly shape our suite of AI-powered products used by industry-leading enterprises. 

Model Development & Research:
Design, develop, and deploy state-of-the-art generative AI models (e.g., GANs, VAEs, Transformers) tailored for enterprise applications.
Stay updated with the latest research trends and innovations in generative AI to continually enhance our solutions.

Integration & Collaboration:
Collaborate with cross-functional teams (data scientists, software engineers, and product managers) to integrate AI models into our SaaS platforms.
Work closely with product teams to translate business requirements into scalable AI features.

Optimization & Deployment:
Perform data preprocessing, feature engineering, and model tuning to ensure high-quality and efficient AI outputs.
Optimize model performance through rigorous testing, hyperparameter tuning, and evaluation using relevant metrics.
Support the deployment and maintenance of AI solutions in production environments.
Implementing feedback mechanisms (collect user input logs, refine model responses, etc.)

Documentation & Best Practices:
Maintain comprehensive documentation of AI models, methodologies, and experiments.
Contribute to the development and refinement of coding standards, testing procedures, and best practices for AI/ML development.

Requirements & Skills:

Educational Background:
Bachelor’s or Master’s degree in Computer Science, Artificial Intelligence, Machine Learning, or a related field.

Professional Experience:
2–4 years of proven, hands-on experience in generative AI 
Demonstrated ability to develop and deploy state-of-the-art generative models using frameworks like TensorFlow or PyTorch.

Technical Prowess:
Natural Language Processing (NLP): Experience with LLMs (e.g., GPT, BERT) and conversational AI frameworks.
Machine Learning & Generative AI: Familiarity with model training, fine-tuning, and deployment.
API Integration: Ability to integrate third-party AI services
Experience building or integrating LLM-based chatbots (e.g., designing prompts, context management, multi-turn conversations) is a plus.
Familiarity with payment integrations and transaction flows is a plus

Soft Skills:
A proactive, innovative mindset paired with a collaborative spirit.
Exceptional problem-solving skills and the ability to articulate complex ideas clearly.
Passion for staying ahead of technological trends and driving forward-thinking solutions.

Contains bad word ""Product Manager"". Skipping this job!
",Skipped,Not Available
**********,https://www.linkedin.com/jobs/view/**********,Pending,2025-07-24 10:40:10.891828,2025-07-25 10:40:11.077298,Found a Bad Word in About Job,"
About the job
Onsurity is a rapidly growing employee healthcare benefits platform that provides flexible and customised healthcare subscriptions for SMEs, start-ups, and enterprises. We believe that access to healthcare benefits shouldn’t be a luxury. It is this philosophy that has strengthened our commitment towards making healthcare affordable and accessible for all.
Our subscriptions include discounts on medicine orders, health checkups, fitness plans, free doctor teleconsultations, and insurance, among other benefits. We believe in inclusivity which means our plans are not limited to full-time employees. We also cover contractual workers, interns, freelancers, and consultants.
We encourage you to read more about us on www.onsurity.com. You can also find us on LinkedIn, Instagram, and YouTube.
Below are stories that define our journey and showcase our commitment to democratizing healthcare across the country.
Onsurity is providing healthcare membership to SMEs with as low as three employees
The Journey Of Startups: Journey Onsurity
Cricketer Anil Kumble backs Onsurity as strategic advisor
Onsurity partners with Gulf Oil to offer healthcare to 10,000 truckers
83% Indian Employees Unaware Of Employer-Provided Healthcare Benefits, Says Study
News: Onsurity secures $45M Series B round led by Creaegis — People Matters

We were also featured in the first season of Disney+ Hotstar's remarkable series, The Great Indian Disruptors.
Our strategic partner and investor, cricketing legend Anil Kumble, is actively involved in our mission to make healthcare more accessible. Anil Kumble recently graced us with his presence at Onsurity’s Bengaluru office and engaged with our employees. He is passionate about our mission and has played an instrumental role in our journey so far.
Recently, Dun & Bradstreet India acknowledged our mission and conferred us with the Dun & Bradstreet Start-up 50 Trailblazer 2023 award.

About the Role:

We are seeking a skilled React Native Developer to join our mobile development team. You will be responsible for building cross-platform mobile applications for iOS and Android using the React Native framework. As part of our tech team, you will collaborate with product managers, designers, and backend developers to deliver high-quality, scalable mobile solutions.

Key Responsibilities:

Develop and maintain cross-platform mobile applications using React Native.
Integrate mobile apps with RESTful APIs and third-party services.
Collaborate with designers to implement intuitive UI/UX designs.
Write clean, maintainable, and well-documented code.
Optimize app performance and troubleshoot issues.
Conduct code reviews and maintain code quality.
Stay up-to-date with emerging technologies and mobile development trends.

Requirements:

2+ years of experience with React Native and JavaScript/TypeScript.
Experience deploying apps to the Apple App Store and Google Play Store.
Solid understanding of mobile architecture, performance optimization, and native device features.
Familiarity with Redux, MobX, or other state management tools.
Experience integrating native modules (e.g., Swift, Objective-C, Java, Kotlin) is a plus.
Understanding of REST APIs, JSON, and offline storage mechanisms.
Familiarity with Git and version control tools.
Excellent problem-solving and communication skills.

Contains bad word ""Product Manager"". Skipping this job!
",Skipped,Not Available
**********,https://www.linkedin.com/jobs/view/**********,Pending,2025-07-23 10:40:32.352604,2025-07-25 10:40:32.571466,Found a Bad Word in About Job,"
About the job
NAGRAVISION, a Kudelski Group company and the world's leading independent provider of content protection and media and entertainment solutions. Our portfolio of award-winning products and services spans traditional video security, cybersecurity, cloud-based video and streaming solutions, turn-key D2C solutions for the sports industry, and rich personalization services that drive subscriber loyalty, our solutions are widely deployed with 520+ operators and 400+ million subscribers, integrated into 500+ different chipsets and 30 partners+ globally, giving providers the ultimate level of choice that best meet their specific needs. For more information, please visit www.nagra.com.

We are seeking a highly skilled and experienced Senior Software Engineer to join our dynamic team in Bangalore. This role offers the opportunity to work on scalable backend systems, mentor junior engineers, and contribute to critical architecture and design decisions.
As a Senior Software Engineer, you will play a key role in building reliable, high-performance software solutions, while driving engineering best practices across the team.

Key Responsibilities:
• Design and develop scalable, asynchronous backend services using Python and FastAPI/Flask
• Work within a Linux-based environment for development and deployment
• Optimize PostgreSQL schemas and queries for high performance
• Implement secure access controls (RBAC, OAuth2) and dynamic configurations
• Lead code reviews, enforce coding standards, and mentor junior developers
• Set up structured logging, monitoring alerts, and caching strategies (Redis)
• Contribute to CI/CD pipelines and infrastructure automation

Required Skills:
• Proficiency in Python, Linux, FastAPI/Flask, PostgreSQL
• Experience with Git and modern CI/CD workflows
• Strong understanding of async programming, test strategies, and scalable system design

Preferred Qualifications:
• Experience with Docker and Kubernetes
• Familiarity with Redis, caching techniques, and handling race conditions
• Exposure to cloud platforms (AWS or Azure), IaC, and security tools like Sentinel, Splunk, or Google SecOps

Contains bad word ""Senior Software Engineer"". Skipping this job!
",Skipped,Not Available
4271889463,https://www.linkedin.com/jobs/view/4271889463,Pending,2025-07-25 10:18:25.017591,2025-07-25 10:41:25.107377,Found a Bad Word in About Job,"
About the job
We’re looking for a creative and skilled Graphic Designer to join our team at Sporthood. This role is perfect for someone who loves sports, understands branding, and knows how to make ideas come alive through design.

Responsibilities:
• Design offline marketing materials – brochures, flyers, standees, banners, etc.
• Create social media creatives tailored for platforms like Instagram, Facebook, and YouTube.
• Work on website visuals – landing page graphics, UI assets, and more.
• Support with visuals for email campaigns, WhatsApp promotions, and other digital platforms.
• Maintain brand consistency across all content formats.
• Collaborate with the content and marketing teams to bring campaigns to life.

Requirements:
• Proficiency in Adobe Creative Suite (Photoshop, Illustrator, InDesign) or similar tools like Canva, Figma, CorelDRAW.
• Strong visual storytelling ability.
• Experience in both digital and print design.
• Good understanding of layout, typography, and color theory.
• Ability to work independently, take ownership, and meet deadlines.

Bonus if you:
• Have experience working in the sports or fitness industry.
• Can also edit videos or create simple motion graphics.
• Are familiar with tools like Premiere Pro, After Effects.

Contains bad word ""Marketing"". Skipping this job!
",Skipped,Not Available
4271888837,https://www.linkedin.com/jobs/view/4271888837,Pending,2025-07-25 10:15:28.673793,2025-07-25 10:41:28.861166,Found a Bad Word in About Job,"
About the job
About Client:
In the 1980s, recognizing opportunities in computer hardware and software, the company established subsidiaries Wipro Infotech and Wipro Systems. In 1981, Wipro developed the first Indian minicomputer based on the Intel 8086 chip in an IISc lab. The software division began in 1984 with the development of a spreadsheet and word-processing suite but shifted to offshore software development in 1990.[12]
By the mid-1990s, Wipro had become one of India's leading manufacturers of personal computers, peripherals, and medical diagnostic equipment.[13] In 1998, the company reported being the second-largest software exporter from India



Job Title : Data Centric testing
Key Skills : Data Testing
Job Locations:pune
Experience : 7+ Years.
Education Qualification: Any Graduation.
Work Mode : Hybrid
Employment Type :Contract.
Notice Period : Immediate


Job Description:
Key Responsibilities:
Design and execute test strategies focused on data quality and accuracy.
Perform ETL testing, data migration testing, and data validation across multiple sources and targets.
Validate data transformations, data loads, and reconciliations in data warehouse environments.
Create SQL queries to verify and validate data at different stages of the ETL process.
Identify data anomalies, mismatches, and integrity issues.
Work with business analysts and developers to understand data requirements and define test cases.
Automate data validation using scripting or testing tools where applicable.
Document test scenarios, test cases, test data, and results.
Collaborate with data engineers, developers, and BI teams to ensure data pipelines and reports meet requirements.
Participate in root cause analysis for data-related defects.
Required Skills:
Strong knowledge of SQL and ability to write complex queries.
Automation framework exp with Azure
Proven experience with python scripting
Understanding of data modeling, data profiling, and data mapping.
Experience with databases like Oracle, SQL Server, PostgreSQL, or Snowflake.
Familiarity with data quality frameworks and best practices.
Experience with test management tools (e.g., JIRA, ALM, TestRail).

Contains bad word ""Business Analyst"". Skipping this job!
",Skipped,Not Available
4271890452,https://www.linkedin.com/jobs/view/4271890452,Pending,2025-07-25 10:13:34.418732,2025-07-25 10:41:34.541158,Found a Bad Word in About Job,"
About the job

Job Title: Video Editor
Experience Required: Minimum 1 year
Location: Bengaluru, Karnataka
Company: Simpel Techlabs Private Limited
Salary: 30k - 35k/month
 
About the Role:
We are looking for a creative and detail-oriented Video Editor to join our team. The ideal candidate should have at least one year of hands-on experience in editing videos for digital platforms, be skilled in post-production tools, and have a strong sense of visual storytelling. You will be working closely with our content and marketing teams to create engaging video content for various platforms.
 
Roles and Responsibilities:
Edit raw footage into polished video content for marketing, branding, social media, internal communication, and client projects.
Add motion graphics, effects, sound, transitions, and other creative elements to enhance video quality.
Collaborate with the creative team to understand project scope and deliver videos aligned with brand guidelines.
Organize and manage media assets and file storage systematically.
Adapt video formats to suit different platforms like Instagram, YouTube, LinkedIn, etc.
Work with tight deadlines while maintaining quality standards.
Stay up to date with industry trends, techniques, and best practices in video editing.
 
Requirements:
Minimum 1 year of experience as a video editor.
Proficiency in video editing tools such as Adobe Premiere Pro, Final Cut Pro, DaVinci Resolve, or similar software.
Basic knowledge of Adobe After Effects or motion graphics tools.
Strong understanding of pacing, timing, and storytelling through video.
Good sense of colour correction, audio editing, and visual aesthetics.
Familiarity with different aspect ratios and export formats.
Ability to work independently as well as collaboratively in a team setup.
Strong organizational and file management skills.

Note: This hiring is for Simpel Techlabs Pvt Ltd.

Contains bad word ""Marketing"". Skipping this job!
",Skipped,Not Available
4271844276,https://www.linkedin.com/jobs/view/4271844276,Pending,2025-07-25 01:41:40.786580,2025-07-25 10:41:40.875810,Found a Bad Word in About Job,"
About the job
About Company

Community of 3000+ creators & studio, powering on-demand video content for enterprise marketers. Helping creators monetize their on/behind camera skills, instead of their audience - to help add passive earnings.

Funding Status

Pre-seed funded.

Early revenue traction.

Founding roles.

Responsibilities

Build the Org, Shape the Goals: Work alongside the founders to streamline internal structures, set OKRs/KPIs, and ensure teams stay on track. 
Own People & Hiring Ops: Co-lead recruitment efforts, onboard new hires, and improve team coordination as we scale. 
Grow Our Video Category: Drive growth in our flagship video content category by aligning supply and demand, ensuring quality and efficiency. 
Launch New Categories: Identify, build, and scale complementary content categories — from research to rollout. 
Break Silos, Drive Execution: Collaborate across teams (Product, Sales, Creators) to remove roadblocks and move fast. 
(Optional) Founders’ Office Projects: Dive into high-impact projects — investor decks, strategic research, or internal process design. 

Skills & Qualifications

0 - 2 years of experience in fast-paced environments (startups, consulting, or similar).
Strong problem-solving ability, attention to detail, and a bias for execution.
Comfortable working directly with leadership and taking full ownership of tasks.

Skills: ownership,collaboration,accountability,growth strategy,execution,skills,building,team coordination,teams,attention to detail,community development,problem-solving,recruitment,growth management,enterprise

Contains bad word ""Sales"". Skipping this job!
",Skipped,Not Available
**********,https://www.linkedin.com/jobs/view/**********,Pending,2025-07-25 00:41:44.769928,2025-07-25 10:41:44.915559,Found a Bad Word in About Job,"
About the job
We are currently hiring Quantum Computing Trainers — passionate educators and experts ready to take students, researchers, and professionals into the next frontier of computational science.
Role Overview

As a Quantum Computing Trainer, you’ll be responsible for educating learners on the principles, tools, and practical applications of quantum computing. You will be an integral part of building the ecosystem — nurturing the next wave of quantum engineers and researchers through workshops, bootcamps, industry-academia programs, and corporate training.

Responsibilities

Design and deliver engaging sessions on quantum computing fundamentals and tools (Qiskit, Cirq, or similar).
Conduct hands-on training using simulators and quantum programming frameworks.
Guide students through capstone projects and real-world quantum use cases.
Stay updated with the latest advancements in quantum computing and pedagogy.
Create content, exercises, and assessment tools tailored to different learning levels.
Collaborate with Aion-IA’s R&D and outreach teams for strategic program alignment.

Requirements

Bachelor’s or Master’s in Physics, Computer Science, Mathematics, or related fields.
Proven experience in Quantum Computing education or training delivery.
Strong understanding of qubits, quantum gates, entanglement, algorithms, and quantum error correction.
Experience with platforms like Qiskit, Cirq, Pennylane, or similar.
Excellent communication and teaching skills.
Prior experience in teaching or mentoring (preferred but not mandatory).

Nice to Have

Certification in Quantum Computing (IBM Qiskit Advocate, Microsoft Q#, etc.)
Research or industry experience in quantum technologies.
Exposure to quantum machine learning or quantum cryptography.

Why Join Aion-IA?

Work with one of India’s leading quantum research companies.
Be at the forefront of building India’s Quantum workforce.
Impact lives by shaping the next-gen tech leaders.
Get involved in real-world R&D, startup collaborations, and national programs.

Contains bad word ""Crypto"". Skipping this job!
",Skipped,Not Available
4271319104,https://www.linkedin.com/jobs/view/4271319104,Pending,2025-07-24 10:41:49.414715,2025-07-25 10:41:49.546077,Found a Bad Word in About Job,"
About the job
Designation - Senior Software Engineer
Location -ITPL Whitefield ( 3 days)
Skills - Java , Spring , Microservices, AWS


Key Responsibilities
● Microservices Development: Build and manage multiple microservices in a
distributed architecture.
● End-to-End Development: Design, develop, and deploy backend solutions from
scratch with high scalability and performance.
● Low-Level Design (LLD): Work on architectural front, ensuring best coding practices
and scalable design patterns.
● System Architecture: Define and implement robust system architecture for complex
applications.
● Hands-on Coding: Solve complex problems using Data Structures and Algorithms
(DSA), ensuring efficient backend solutions.
● Collaboration: Work closely with cross-functional teams, including product managers,
frontend engineers, and DevOps teams.
Required Skills & Experience
● Java with deep expertise in backend development.
● Spring Boot & Microservices – Experience in building and managing multiple
microservices.
● Experience in building applications from scratch – Strong expertise in designing and
implementing new systems.
● Low-Level Design (LLD) & Architectural Thinking – Ability to define and implement
scalable architecture.
● Data Structures & Algorithms (DSA) – Hands-on coding skills with problem-solving
abilities.
● System Architecture – Experience in designing high-performance, scalable backend
solutions.

Contains bad word ""Senior Software Engineer"". Skipping this job!
",Skipped,Not Available
4268561706,https://www.linkedin.com/jobs/view/4268561706,Pending,2025-07-23 10:41:55.820902,2025-07-25 10:41:55.947332,Found a Bad Word in About Job,"
About the job
This role is for one of the Weekday's clients

Salary range: Rs 2000000 - Rs 3500000 (ie INR 20-35 LPA)

Min Experience: 3 years

Location: Bengaluru

JobType: full-time

We are seeking an experienced and passionate iOS Developer to join our team and contribute to the development of high-quality mobile applications. In this role, you'll build and maintain user-facing iOS apps using Swift, SwiftUI, and UIKit. You'll collaborate with cross-functional teams to deliver seamless user experiences and ensure the stability, performance, and scalability of the applications.

Requirements

Key Responsibilities:

 Design, develop, and maintain robust iOS applications using Swift, SwiftUI, and UIKit. 
 Integrate RESTful APIs and ensure smooth communication with backend microservices. 
 Participate actively in Agile ceremonies including sprint planning, stand-ups, and retrospectives. 
 Collaborate closely with product managers, backend developers, QA engineers, and designers. 
 Conduct thorough code reviews, write unit tests, and maintain high standards for code quality. 
 Contribute to CI/CD processes using GitHub and related tooling. 
 Support releases, monitor app performance, troubleshoot crashes, and iterate based on user feedback. 
 Participate in architectural discussions and help manage technical debt. 

Qualifications:

 3-4+ years of hands-on experience building iOS applications for end users. 
 Expertise in Swift, SwiftUI, and UIKit. 
 Proficient in integrating RESTful APIs and handling asynchronous networking. 
 Solid understanding of debugging, profiling, and monitoring tools for mobile apps. 
 Experience with automated testing, build, and deployment pipelines. 
 Strong attention to detail and passion for writing clean, maintainable, and well-documented code. 
 Excellent communication and teamwork skills. 

Preferred Skills:

 Familiarity with GitHub-based workflows. 
 Experience working in fast-paced Agile environments

Contains bad word ""Product Manager"". Skipping this job!
",Skipped,Not Available
4268554101,https://www.linkedin.com/jobs/view/4268554101,Pending,2025-07-23 10:42:33.020209,2025-07-25 10:42:33.131558,Found a Bad Word in About Job,"
About the job
About Marvell

Marvell’s semiconductor solutions are the essential building blocks of the data infrastructure that connects our world. Across enterprise, cloud and AI, automotive, and carrier architectures, our innovative technology is enabling new possibilities.

At Marvell, you can affect the arc of individual lives, lift the trajectory of entire industries, and fuel the transformative potential of tomorrow. For those looking to make their mark on purposeful and enduring innovation, above and beyond fleeting trends, Marvell is a place to thrive, learn, and lead.

Your Team, Your Impact

Marvell is a leading provider of innovative technologies, including ultra-fast read channels, high-performance processors, leading edge transceivers, highly efficient analog designs, and powerful cryptographic engines. These solutions address all segments of the hard disk drive (HDD) and solid-state drive (SSD) electronics markets, providing complete solutions including controllers, product firmware, and reference board designs. Many of the same technologies have been utilized in Marvell system solutions products, powering PCs, servers, cloud, and enterprise systems.

What You Can Expect

Technical leader on a fast-paced project team of R&D engineers involved in the development of cloud Datacenter products of Marvell packet processors family.
Contribute to and lead all phases of software development from requirement gathering through architect/design, implementation, UT/Functional testing and maintaining multiple software components.
Contribute to the development of aggressive project goals and schedules.
Demonstrate technical leadership and execute projects across a highly distributed engineering team.
Co-ordinate and work with various cross-functional groups such as Architect, other development teams, QA/Validation, System Test, Product Management, Product Documentation, Customer teams
Responsible to mentor and train the team
Provide necessary support with customers using our solutions.

What We're Looking For

Bachelor’s/Master's in computer science/ECE with 5 to 20 Years of relevant experience.
Extensive technical depth in L2 and L3 Ethernet switching and routing protocols.
Extensive experience in programming languages such as C/C++/Python
Extensive experience in SDK development in Switching/Routing ASIC
Hands on experience on switch/router embedded system software development
Experience architecting innovative, scalable Linux based Embedded products.
Ability to communicate technical concepts to a wide range of audiences spanning executives to junior engineers
Proven ability to successfully lead distributed teams and run projects across multiple locations
Working experience in L2 switching/forwarding areas such as LAG, VLAN, xSTP, LLDP, Link OAM, ARP, VxLAN, DC fabric switching, etc
Working experience in L3 unicast/multicast routing protocols such as RIP, OSPF, IGMP, BGP, ISIS, etc
Working experience in areas like ACL, QoS, Policers, TCAM, etc
Experience in Linux kernel, SERDES, board bring up etc.
Excellent written and verbal communication skills with the ability to present complex technical information in a clear and concise manner to a variety of audiences.
Ability to work independently with minimal guidance.
Ability to grasp new requirements and solutions based on customer/industry needs
Work cohesively in team environment and with geographically disperse teams.

Additional Compensation And Benefit Elements

With competitive compensation and great benefits, you will enjoy our workstyle within an environment of shared collaboration, transparency, and inclusivity. We’re dedicated to giving our people the tools and resources they need to succeed in doing work that matters, and to grow and develop with us. For additional information on what it’s like to work at Marvell, visit our Careers page.

All qualified applicants will receive consideration for employment without regard to race, color, religion, sex, national origin, sexual orientation, gender identity, disability or protected veteran status.

Contains bad word ""Technical Lead"". Skipping this job!
",Skipped,Not Available
4270717191,https://www.linkedin.com/jobs/view/4270717191,Pending,2025-07-23 10:42:36.569144,2025-07-25 10:42:36.812378,Found a Bad Word in About Job,"
About the job
About the Role
We’re looking for a Web Developer who’s part builder, part problem-solver, and 100% go-getter. You’ll work on everything from websites to AI tools — collaborating with creatives, marketers, and clients to bring ideas to life fast.

Key Responsibilities
Website Development:
Build, maintain, and optimize responsive websites (front-end & back-end).
Work across platforms such as WordPress, Shopify, Webflow, or custom-coded environments.
Ensure clean, scalable, and well-documented code using HTML, CSS, JavaScript, PHP, Python, or other relevant web technologies.

App Development:
Develop and maintain web-based or hybrid mobile applications (iOS & Android).
Collaborate with UI/UX teams to bring creative concepts to life.

SEO, GEO & SEvO & Optimization:
Implement On-page & Off-page SEO best practices.
Integrate SEO tools and schema markup.
Work with AI-based Generative Engine Optimization strategies (GEO) to increase discoverability.

AI & Agentic AI Integration:
Build and integrate AI models (LLMs, chatbots, APIs) to enhance user experience.
Develop intelligent agents that support automation and personalization across platforms.
Interest in finding solutions using AI 

Email Marketing:
Create and optimize email templates compatible across platforms.
Manage campaigns through tools like Saleshandy, Mailchimp, Klaviyo, etc.

Collaboration with Teams:
Work with design, strategy, and media teams to align development goals with campaign objectives.
Ensure seamless integration of visual and interactive elements in marketing campaigns.


Requirements
2–5 years of hands-on experience in web development & digital marketing
Proficiency in modern front-end and back-end technologies (React, Vue, Angular, Node.js, Laravel, etc.).
Solid experience with CMS platforms (WordPress, Shopify, etc.).
Exposure to integrating or building AI tools (OpenAI, LangChain, etc.) is a must.
Strong grasp of SEO (on-page/off-page) and Generative Engine Optimization concepts.
Experience with tools like Google Analytics, Tag Manager, Search Console, etc.
Familiarity with running and tracking email marketing campaigns.
Bonus: Knowledge of digital media and ad tech ecosystems.


What You’ll Get Out of It
Work directly with top brands and founders — real impact, no fluff.
Learn how to build AI agents and tools that transform how work gets done.
Be part of an award-winning team that blends creativity, performance, and innovation.
Exposure to end-to-end brand building — from pitch to launch.
A high-growth environment where your ideas are heard and built.
The satisfaction of seeing your work go live — quickly and proudly.

Contains bad word ""Sales"". Skipping this job!
",Skipped,Not Available
**********,https://www.linkedin.com/jobs/view/**********,Pending,2025-07-24 13:42:42.006465,2025-07-25 10:42:42.125328,Found a Bad Word in About Job,"
About the job
Job Opportunity at Teslon Technologies Pvt. Ltd.

Teslon Technologies Pvt. Ltd., a healthcare-based startup located in Electronic City Phase 1, is looking for passionate and motivated individuals to join our team.

Position: Technical Marketing Engineer (BDA)

Eligibility Requirements:

Any graduate with strong communication skills.
Basic knowledge of Excel and PowerPoint.
Ability to quickly understand the products and their working principles.
Freshers are preferred, but candidates with experience are welcome to apply.
Willingness to relocate to the office nearest location.

Additional Information:

Saturdays and Sundays are off.
salary based on interview performance.

If you are eager to grow with an innovative healthcare startup and meet the above requirements, apply now! Join us at Teslon Technologies and be a part of our exciting journey in the healthcare industry.

Contains bad word ""Marketing"". Skipping this job!
",Skipped,Not Available
**********,https://www.linkedin.com/jobs/view/**********,Pending,2025-07-24 13:42:47.864611,2025-07-25 10:42:48.000169,Found a Bad Word in About Job,"
About the job
Job Title: Sound Engineer
Location: Bangalore
Position: Full-time
Years of experience: Fresher - 1 years
About us:
Artificial Labs stands at the forefront of Generative AI, collaborating with some of the industry’s biggest brands across India, the Middle East, and Southeast Asia. We’re transforming the way brands tell stories, pushing the boundaries of advertising, digital marketing, and filmmaking through AI-powered visual creativity

Key responsibilities:
Compose, mix, and produce high-quality music tracks, blending AI-generated elements with human creativity.
Experiment with innovative sound design techniques to create immersive audio experiences and explore new ways of integrating AI into music production.
Edit, enhance, and mix music tracks for various projects, ensuring clarity, balance, and high audio quality.
Collaborate with creative and brand teams to understand project requirements, incorporate feedback and meet deadlines
Stay updated on industry trends, emerging technologies, and new software tools for audio production.

Skills we're looking for:
·  Proficient in using FL Studio software
· Experience in music production, composing and producing original melody ideas for jingles
· Familiar with AI tools like Suno, Udio, ElevenLabs for voiceovers and integrate AI
· Able to combine AI tools with manual production for best results
· Knowledge of mixing and mastering techniques
· Understanding of how audio frequencies impact sound and tonality
· Familiarity with melody programming and music arrangement
· Proficient in audio editing and audio cleaning

What’s in it for you?
A chance to work at the cutting-edge of new-age media. You will be building music assets for some of India’s and the world’s leading brands. A brand-new learning curve and the chance to work in a fast-growing global organization. What we can also promise you is a fun and dynamic workplace filled with people that want to build the future of media. 

Please submit your resume, along with a portfolio or samples of your <NAME_EMAIL>

Contains bad word ""Marketing"". Skipping this job!
",Skipped,Not Available
