Job ID,Job Link,Resume Tried,Date listed,Date Tried,Assumed Reason,Stack Trace,External Job link,Screenshot Name
4272017952,https://www.linkedin.com/jobs/view/4272017952,Pending,2025-07-25 16:34:56.347416,2025-07-25 17:34:56.458735,Found a Bad Word in About Job,"
About the job
Type: Contract (C2H)
Duration: 6 Months 
Location: Bangalore (Hybrid 3 Days )
Rate: 18 LPA
Experience: 6 to 9 Years

Looking for an experienced Hyperion support Developer with 5-8 years of relevant experience. Candidates with financial background is preferred. The detailed responsibilities are mentioned below.
HFM, Planning, Essbase, HPCM
Perform coding and configuration to enhance and maintain Oracle EPM tools or Hyperion applications, including Planning, Essbase (BSO and ASO cubes), FDMEE,HPCM.
Monitor, maintain security, management process controls, task flows, business rules, scripts, member lists, journal module, objects (Webforms, Grids, Task lists), consolidation and data clearing procedures, metadata updates etc.
Strong knowledge of Essbase scripting and Calc Manager Rules, Essbase Calc Scripts, Batch scripts, MDX & MAXL. EPM solution, Essbase Cubes.
Work closely with Master data team and finance teams to manage metadata changes, business rule updates, form/dashboard enhancements, and data loads.
Have good general functional knowledge and understanding of budgeting, forecasting and financial analysis and close processes
Sound knowledge of Life Cycle Management, user Management, Security Management etc
Provide level one and level two support in line with the team’s remit: CoA validations and deployments using EPMA / DRM; User support and setups.
Good analytical, problem solving, & communication skills
Have experience working with ServiceNow ticketing tool, approval process and SLA.
Address user queries with webforms, SmartView, data reconciliation in HFM
 Read, understand and update HFM rules per user requirements
 Update HFM application metadata and security as required
 Re-shell application and reconcile data for regression testing before monthly updates.
 Deploy updates to Pre-Prod and reconcile data before monthly updates in HFM and Planning/Essbase apps.
 Deploy changes to Production after preproduction COA and new enhancements testing.
 FDMEE: Monitor data loading schedule jobs and if any failures/kickouts. Address these by liaising with entity owners (in the case of data quality errors) or updating dimension maps

Contains bad word ""Finance"". Skipping this job!
",Skipped,Not Available
**********,https://www.linkedin.com/jobs/view/**********,Pending,2025-07-25 16:36:20.520550,2025-07-25 17:36:20.678226,Found a Bad Word in About Job,"
About the job
About Us
MyRemoteTeam, Inc is a fast-growing distributed workforce enabler, helping companies scale with top global talent. We empower businesses by providing world-class software engineers, operations support, and infrastructure to help them grow faster and better.

Job Title: Full Stack Developer – Java + React
Experience: 5+ Years

Job Summary:
We are looking for a talented and experienced Full Stack Developer (Java + React) with a strong background in building scalable, high-quality, and high-performance web applications. The ideal candidate will have hands-on experience in Java, Spring Boot, Microservices architecture, and React.js.

Key Responsibilities:
Design, develop, and maintain scalable backend services using Java, Spring Boot, and Microservices.
Develop intuitive and responsive frontend interfaces using React.js.
Collaborate with product managers, UI/UX designers, and other developers to understand requirements and translate them into technical solutions.
Ensure code quality through unit testing, code reviews, and best practices.
Integrate APIs and work with third-party services as needed.
Optimize application performance and scalability.
Participate in Agile/Scrum development cycles, including sprint planning and daily stand-ups.
Troubleshoot and resolve production issues in a timely manner.

Required Skills:
5+ years of experience in Java development
Strong experience with Spring Boot and building RESTful APIs
Solid understanding of Microservices architecture
2+ years of hands-on experience with React.js, JavaScript, HTML5, and CSS3
Experience working with databases like MySQL, PostgreSQL, or MongoDB
Familiarity with version control systems (Git)
Understanding of CI/CD pipelines and deployment processes
Good problem-solving skills and attention to detail

Contains bad word ""Product Manager"". Skipping this job!
",Skipped,Not Available
4275596055,https://www.linkedin.com/jobs/view/4275596055,Pending,2025-07-25 16:36:26.786024,2025-07-25 17:36:26.930898,Required experience is high,"
About the job
We have a Walk-In Drive for Java Full Stack Developer requirement on 28th&29th July 25.

Drive venue:
Address: Adobe Tower, Block A, Prestige Platina Tech Park, Marathahalli-Sarjapur Outer Ring Road, Kadubeesanahalli



Java Full Stack Developer: 

Core Java with Microservices - 40%
Cloud(Azure/AWS) - 20%
Rest API integration - 10%
Angular/ReactJS - 10%
SQL - 10%
SpringBoot - 10%


Location : Bengaluru - Hybrid
NP : 30days
Experience :6 to 10 yrs
Budget : Max 30 LPA
Payroll : STL - Sterlite Technologies Limited

JD :
Job Title : Java Full Stack Developer
Work Location : Bengaluru
Experience : 6-8 Yrs.
Job Mode : Hybrid
Responsibilities:
Candidate must have hands-on implementation experience with Java.
Candidate will need to work on Design and Development of business-critical solutions using cloud-based services on AWS/Azure.
Candidate will need to work closely with other teams in IT including Business Solution Analyst, QA, Release Management, and Operations.
Candidate will be responsible for collaborating with other engineers and project stakeholders on the design and development of solutions and integrations.
Candidate will be responsible for building and unit testing the integrations using the appropriate technology. They will work closely with the testing team to validate their integration, and with Operations to investigate, troubleshoot, and address production issues.
The candidate should have a minimum of 6+ years of overall IT experience with at least last 3+ years on Java development and frameworks mainly Spring.
Experience developing and managing RESTful API applications using microservices.
Demonstrable Experience in developing scalable Cloud based services on AWS/Azure.
Experience in Javascript, React would be an added advantage.
Experience in SQL and database concepts.
Proficiency in data structures and algorithms.
Experience troubleshooting and finding multiple solutions to a complex problem.
Excellent communication skills and demonstrated ability to effectively communicate technical issues and resolve problems.
Experience in delivering IT solutions using Agile (Scrum) project methodology and practices.

Experience required 6 > Current Experience 5. Skipping this job!
",Skipped,Not Available
4275590038,https://www.linkedin.com/jobs/view/4275590038,Pending,2025-07-25 15:36:30.292198,2025-07-25 17:36:30.430179,Found a Bad Word in About Job,"
About the job
Job Title: Senior Software Architect
Location: India (Remote/Hybrid)
Experience: 5–8 years
Education: BS/MS in Computer Science or equivalent

About the Role
We’re seeking a high-impact Senior Software Architect to design, lead, and deliver core product features and scalable systems. In this role, you will mentor engineers, drive technical excellence, and own the full software development lifecycle — from design to deployment.

Key Responsibilities
Architect and design scalable, distributed systems for high-performance cloud environments.
Lead critical product features and core infrastructure projects.
Mentor and guide engineers through design/code reviews and best practices.
Own end-to-end SDLC: from requirements gathering to design, development, testing, deployment, and support.
Collaborate across global teams to deliver innovative and reliable software solutions.
Continuously improve system performance, scalability, and resilience.

Technical Expertise
Strong background in Java, Big Data, and Distributed Systems.
Proficient in Microservices Architecture and Cloud platforms (AWS, Azure).
Deep knowledge of Algorithms, Data Structures, and system design principles.
Hands-on experience with NoSQL databases, Hadoop ecosystem (HDFS, Hive, etc.).

What We’re Looking For
5–8 years of experience in software engineering, including system architecture and cloud-based design.
A proven track record of driving complex projects from concept to production.
Passion for mentoring, technical leadership, and continuous improvement.
Excellent communication and collaboration skills in a fast-paced, global team environment.

Contains bad word ""Technical Lead"". Skipping this job!
",Skipped,Not Available
4272031343,https://www.linkedin.com/jobs/view/4272031343,Pending,2025-07-25 15:36:33.776555,2025-07-25 17:36:33.895079,Found a Bad Word in About Job,"
About the job
Senior Software Engineer - Backend
FinacPlus provide virtual business process services to overseas clients and this position is to be part of the dedicated team which providesmortgage back-office servicesand software productdevelopment to Toorak Capital Partners https://www.toorakcapital.com/ from Mortgage FinanceIndustry.

About Toorak Capital

Toorak Capital Partnersis an integrated lending platformthat funds small business purposeresidential, multifamily and mixed-use loans throughout the U.S. and the United Kingdom.

Headquartered in Summit, N.J., Toorak Capital Partners acquires and manages loans directly from private lendersthat originate high credit quality loans.

RESPONSIBILITIES
To Work as Backend Developerin developing Cloud Based Web Applications
To be part of team working on various types of web applications related to MortgageFinance.
Experience in solving a real-world problem of Implementing, Designing and helping develop a new Enterprise class Product from ground-up.
You have expertise in the AWS Cloud Infrastructure and Micro-services architecture around the AWS Service stack like Lambdas, SQS, SNS, MySQL Databases along with Dockers and containerized solutions/applications.
Experienced in Relational and No-SQL databases and scalable design.
Experience in solvingchallenging problems by developing elegant,maintainable code.
Delivered rapid iterations of software based on user feedback and metrics.
Help the team make key decisions on our productand technology direction.
You actively contribute to the adoptionof frameworks, standards, and new technologies.

QUALIFICATIONS AND EXPERIENCE
Overall 5 to 7 years of experience. Node.js experience is must.
At least 3+ years of experience or couple of large-scale productsdelivered on microservices.
Strong design skillson microservices and AWS platforminfrastructure.
Excellent programming skill in Python,Node.js and Java script.
Hands on development in rest API’s.
Good understanding of nuances of distributed systems,scalability, and availability.

Location: Bangalore
Timings: 11am To 8pm IST
Salary Range: Best in Industry
Send Application To: <EMAIL>

Contains bad word ""Senior Software Engineer"". Skipping this job!
",Skipped,Not Available
4275582302,https://www.linkedin.com/jobs/view/4275582302,Pending,2025-07-25 14:36:37.021218,2025-07-25 17:36:37.227059,Found a Bad Word in About Job,"
About the job
Job Title : C++ Developer
Location State : Karnataka
Location City : Bangalore
Experience Required : 2+ Year(s)
Shift: General
Work Mode: Hybrid
Position Type: Contract

Company Name: VARITE INDIA PRIVATE LIMITED

About The Client: An American multinational digital communications technology conglomerate corporation headquartered in San Jose, California. The Client develops, manufactures, and sells networking hardware, software, telecommunications equipment, and other high-technology services and products. The Client specializes in specific tech markets, such as the Internet of Things (IoT), domain security, videoconferencing, and energy management. It is one of the largest technology companies in the world, ranking 82nd on the Fortune 100 with over $51 billion in revenue and nearly 83,300 employees.

Essential Job Functions:
Work on Linux-based platforms and understand open-source processes.
Solve complex problems using strong troubleshooting skills.
Communicate and collaborate effectively, both verbally and in writing.
Handle ambiguity and prioritize tasks effectively.
Define problems, analyze facts, and develop logical solutions.
Foster teamwork and resolve issues positively.

Qualifications:
Experience Range: 2 to 3 years

Skills Required:
Programming Languages: C/C++.
Platform: Linux
Core Concepts: Multithreading, Singleton, Algorithms, Data Structures, Object-Oriented Design, and Database concepts.
Asynchronous Transactions: REST API, Event-driven patterns, IPC, and HTTP transactions.
Security: TLS, mTLS, certificate management, and ciphers.
Networking Fundamentals: IP, TCP/UDP, DNS, HTTP.
Troubleshooting: Debugging functional, scale, and threading issues in Linux OS.
Agile Experience: Familiarity with Agile development environments.

How to Apply: Interested candidates are invited to submit their resume using the apply online button on this job post.

Equal Opportunity Employer: VARITE is an equal opportunity employer. We celebrate diversity and are committed to creating an inclusive environment for all employees. We do not discriminate on the basis of race, color, religion, sex, sexual orientation, gender identity or expression, national origin, age, marital status, veteran status, or disability status.

Unlock Rewards: Refer Candidates and Earn. If you're not available or interested in this opportunity, please pass this along to anyone in your network who might be a good fit and interested in our open positions. VARITE offers a Candidate Referral program, where you'll receive a one-time referral bonus based on the following scale if the referred candidate completes a three-month assignment with VARITE.

Exp Req - Referral Bonus
0 - 2 Yrs. - INR 5,000
2 - 6 Yrs. - INR 7,500
6 + Yrs. - INR 10,000

About VARITE: VARITE is a global staffing and IT consulting company providing technical consulting and team augmentation services to Fortune 500 Companies in USA, UK, CANADA and INDIA. VARITE is currently a primary and direct vendor to the leading corporations in the verticals of Networking, Cloud Infrastructure, Hardware and Software, Digital Marketing and Media Solutions, Clinical Diagnostics, Utilities, Gaming and Entertainment, and Financial Services.

Contains bad word ""Marketing"". Skipping this job!
",Skipped,Not Available
**********,https://www.linkedin.com/jobs/view/**********,Pending,2025-07-25 16:38:15.226580,2025-07-25 17:38:15.379620,Found a Bad Word in About Job,"
About the job
About Us
MyRemoteTeam, Inc is a fast-growing distributed workforce enabler, helping companies scale with top global talent. We empower businesses by providing world-class software engineers, operations support, and infrastructure to help them grow faster and better.

Job Title: Full Stack Developer – Java + React
Experience: 5+ Years

Job Summary:
We are looking for a talented and experienced Full Stack Developer (Java + React) with a strong background in building scalable, high-quality, and high-performance web applications. The ideal candidate will have hands-on experience in Java, Spring Boot, Microservices architecture, and React.js.

Key Responsibilities:
Design, develop, and maintain scalable backend services using Java, Spring Boot, and Microservices.
Develop intuitive and responsive frontend interfaces using React.js.
Collaborate with product managers, UI/UX designers, and other developers to understand requirements and translate them into technical solutions.
Ensure code quality through unit testing, code reviews, and best practices.
Integrate APIs and work with third-party services as needed.
Optimize application performance and scalability.
Participate in Agile/Scrum development cycles, including sprint planning and daily stand-ups.
Troubleshoot and resolve production issues in a timely manner.

Required Skills:
5+ years of experience in Java development
Strong experience with Spring Boot and building RESTful APIs
Solid understanding of Microservices architecture
2+ years of hands-on experience with React.js, JavaScript, HTML5, and CSS3
Experience working with databases like MySQL, PostgreSQL, or MongoDB
Familiarity with version control systems (Git)
Understanding of CI/CD pipelines and deployment processes
Good problem-solving skills and attention to detail

Contains bad word ""Product Manager"". Skipping this job!
",Skipped,Not Available
4275596055,https://www.linkedin.com/jobs/view/4275596055,Pending,2025-07-25 16:38:21.564466,2025-07-25 17:38:21.751193,Required experience is high,"
About the job
We have a Walk-In Drive for Java Full Stack Developer requirement on 28th&29th July 25.

Drive venue:
Address: Adobe Tower, Block A, Prestige Platina Tech Park, Marathahalli-Sarjapur Outer Ring Road, Kadubeesanahalli



Java Full Stack Developer: 

Core Java with Microservices - 40%
Cloud(Azure/AWS) - 20%
Rest API integration - 10%
Angular/ReactJS - 10%
SQL - 10%
SpringBoot - 10%


Location : Bengaluru - Hybrid
NP : 30days
Experience :6 to 10 yrs
Budget : Max 30 LPA
Payroll : STL - Sterlite Technologies Limited

JD :
Job Title : Java Full Stack Developer
Work Location : Bengaluru
Experience : 6-8 Yrs.
Job Mode : Hybrid
Responsibilities:
Candidate must have hands-on implementation experience with Java.
Candidate will need to work on Design and Development of business-critical solutions using cloud-based services on AWS/Azure.
Candidate will need to work closely with other teams in IT including Business Solution Analyst, QA, Release Management, and Operations.
Candidate will be responsible for collaborating with other engineers and project stakeholders on the design and development of solutions and integrations.
Candidate will be responsible for building and unit testing the integrations using the appropriate technology. They will work closely with the testing team to validate their integration, and with Operations to investigate, troubleshoot, and address production issues.
The candidate should have a minimum of 6+ years of overall IT experience with at least last 3+ years on Java development and frameworks mainly Spring.
Experience developing and managing RESTful API applications using microservices.
Demonstrable Experience in developing scalable Cloud based services on AWS/Azure.
Experience in Javascript, React would be an added advantage.
Experience in SQL and database concepts.
Proficiency in data structures and algorithms.
Experience troubleshooting and finding multiple solutions to a complex problem.
Excellent communication skills and demonstrated ability to effectively communicate technical issues and resolve problems.
Experience in delivering IT solutions using Agile (Scrum) project methodology and practices.

Experience required 6 > Current Experience 5. Skipping this job!
",Skipped,Not Available
4275590038,https://www.linkedin.com/jobs/view/4275590038,Pending,2025-07-25 15:38:25.303958,2025-07-25 17:38:25.487402,Found a Bad Word in About Job,"
About the job
Job Title: Senior Software Architect
Location: India (Remote/Hybrid)
Experience: 5–8 years
Education: BS/MS in Computer Science or equivalent

About the Role
We’re seeking a high-impact Senior Software Architect to design, lead, and deliver core product features and scalable systems. In this role, you will mentor engineers, drive technical excellence, and own the full software development lifecycle — from design to deployment.

Key Responsibilities
Architect and design scalable, distributed systems for high-performance cloud environments.
Lead critical product features and core infrastructure projects.
Mentor and guide engineers through design/code reviews and best practices.
Own end-to-end SDLC: from requirements gathering to design, development, testing, deployment, and support.
Collaborate across global teams to deliver innovative and reliable software solutions.
Continuously improve system performance, scalability, and resilience.

Technical Expertise
Strong background in Java, Big Data, and Distributed Systems.
Proficient in Microservices Architecture and Cloud platforms (AWS, Azure).
Deep knowledge of Algorithms, Data Structures, and system design principles.
Hands-on experience with NoSQL databases, Hadoop ecosystem (HDFS, Hive, etc.).

What We’re Looking For
5–8 years of experience in software engineering, including system architecture and cloud-based design.
A proven track record of driving complex projects from concept to production.
Passion for mentoring, technical leadership, and continuous improvement.
Excellent communication and collaboration skills in a fast-paced, global team environment.

Contains bad word ""Technical Lead"". Skipping this job!
",Skipped,Not Available
4272031343,https://www.linkedin.com/jobs/view/4272031343,Pending,2025-07-25 15:38:28.782117,2025-07-25 17:38:28.890001,Found a Bad Word in About Job,"
About the job
Senior Software Engineer - Backend
FinacPlus provide virtual business process services to overseas clients and this position is to be part of the dedicated team which providesmortgage back-office servicesand software productdevelopment to Toorak Capital Partners https://www.toorakcapital.com/ from Mortgage FinanceIndustry.

About Toorak Capital

Toorak Capital Partnersis an integrated lending platformthat funds small business purposeresidential, multifamily and mixed-use loans throughout the U.S. and the United Kingdom.

Headquartered in Summit, N.J., Toorak Capital Partners acquires and manages loans directly from private lendersthat originate high credit quality loans.

RESPONSIBILITIES
To Work as Backend Developerin developing Cloud Based Web Applications
To be part of team working on various types of web applications related to MortgageFinance.
Experience in solving a real-world problem of Implementing, Designing and helping develop a new Enterprise class Product from ground-up.
You have expertise in the AWS Cloud Infrastructure and Micro-services architecture around the AWS Service stack like Lambdas, SQS, SNS, MySQL Databases along with Dockers and containerized solutions/applications.
Experienced in Relational and No-SQL databases and scalable design.
Experience in solvingchallenging problems by developing elegant,maintainable code.
Delivered rapid iterations of software based on user feedback and metrics.
Help the team make key decisions on our productand technology direction.
You actively contribute to the adoptionof frameworks, standards, and new technologies.

QUALIFICATIONS AND EXPERIENCE
Overall 5 to 7 years of experience. Node.js experience is must.
At least 3+ years of experience or couple of large-scale productsdelivered on microservices.
Strong design skillson microservices and AWS platforminfrastructure.
Excellent programming skill in Python,Node.js and Java script.
Hands on development in rest API’s.
Good understanding of nuances of distributed systems,scalability, and availability.

Location: Bangalore
Timings: 11am To 8pm IST
Salary Range: Best in Industry
Send Application To: <EMAIL>

Contains bad word ""Senior Software Engineer"". Skipping this job!
",Skipped,Not Available
4275582302,https://www.linkedin.com/jobs/view/4275582302,Pending,2025-07-25 14:38:32.265198,2025-07-25 17:38:32.418401,Found a Bad Word in About Job,"
About the job
Job Title : C++ Developer
Location State : Karnataka
Location City : Bangalore
Experience Required : 2+ Year(s)
Shift: General
Work Mode: Hybrid
Position Type: Contract

Company Name: VARITE INDIA PRIVATE LIMITED

About The Client: An American multinational digital communications technology conglomerate corporation headquartered in San Jose, California. The Client develops, manufactures, and sells networking hardware, software, telecommunications equipment, and other high-technology services and products. The Client specializes in specific tech markets, such as the Internet of Things (IoT), domain security, videoconferencing, and energy management. It is one of the largest technology companies in the world, ranking 82nd on the Fortune 100 with over $51 billion in revenue and nearly 83,300 employees.

Essential Job Functions:
Work on Linux-based platforms and understand open-source processes.
Solve complex problems using strong troubleshooting skills.
Communicate and collaborate effectively, both verbally and in writing.
Handle ambiguity and prioritize tasks effectively.
Define problems, analyze facts, and develop logical solutions.
Foster teamwork and resolve issues positively.

Qualifications:
Experience Range: 2 to 3 years

Skills Required:
Programming Languages: C/C++.
Platform: Linux
Core Concepts: Multithreading, Singleton, Algorithms, Data Structures, Object-Oriented Design, and Database concepts.
Asynchronous Transactions: REST API, Event-driven patterns, IPC, and HTTP transactions.
Security: TLS, mTLS, certificate management, and ciphers.
Networking Fundamentals: IP, TCP/UDP, DNS, HTTP.
Troubleshooting: Debugging functional, scale, and threading issues in Linux OS.
Agile Experience: Familiarity with Agile development environments.

How to Apply: Interested candidates are invited to submit their resume using the apply online button on this job post.

Equal Opportunity Employer: VARITE is an equal opportunity employer. We celebrate diversity and are committed to creating an inclusive environment for all employees. We do not discriminate on the basis of race, color, religion, sex, sexual orientation, gender identity or expression, national origin, age, marital status, veteran status, or disability status.

Unlock Rewards: Refer Candidates and Earn. If you're not available or interested in this opportunity, please pass this along to anyone in your network who might be a good fit and interested in our open positions. VARITE offers a Candidate Referral program, where you'll receive a one-time referral bonus based on the following scale if the referred candidate completes a three-month assignment with VARITE.

Exp Req - Referral Bonus
0 - 2 Yrs. - INR 5,000
2 - 6 Yrs. - INR 7,500
6 + Yrs. - INR 10,000

About VARITE: VARITE is a global staffing and IT consulting company providing technical consulting and team augmentation services to Fortune 500 Companies in USA, UK, CANADA and INDIA. VARITE is currently a primary and direct vendor to the leading corporations in the verticals of Networking, Cloud Infrastructure, Hardware and Software, Digital Marketing and Media Solutions, Clinical Diagnostics, Utilities, Gaming and Entertainment, and Financial Services.

Contains bad word ""Marketing"". Skipping this job!
",Skipped,Not Available
4270157274,https://www.linkedin.com/jobs/view/4270157274,Pending,2025-07-25 14:38:38.219295,2025-07-25 17:38:38.317561,Found a Bad Word in About Job,"
About the job
Job description

We are looking for an experienced Android Developer to join our dynamic team. The ideal candidate will be responsible for designing, developing, and deploying Android applications to deliver innovative and intelligent user experiences.

Key Responsibilities:

Develop and maintain Android applications using Android Studio.
Support and debug cross-platform applications with Xcode (for iOS builds).
Collaborate with designers and product managers to create intuitive, user-friendly mobile experiences.
Integrate third-party APIs and SDKs.
Ensure performance, quality, and responsiveness of applications.
Write clean, maintainable code following best practices (MVVM, Clean Architecture, etc.).
Manage code using version control systems like Git.
Conduct testing and debugging across multiple device types and OS versions.
Participate in code reviews and contribute to team knowledge sharing.

Qualifications:

Bachelors or Masters degree in Computer Science, Engineering, or a related field.
2 years of experience as Andriod Developer
Experience in Android app development using Kotlin and/or Java,
Proficient in integrating RESTful APIs and third-party SDKs.
Strong understanding of Android UI/UX principles, design patterns (MVVM, MVP, etc.)
Proficiency in Android Studio.
Familiarity with Xcode and the iOS build/debug process.
Understanding of mobile UI/UX principles and best practices.
Experience consuming RESTful APIs and working with JSON.
Familiar with version control tools such as Git.
Experience with modern architecture patterns (MVVM, Clean Architecture).

Contains bad word ""Product Manager"". Skipping this job!
",Skipped,Not Available
4272009471,https://www.linkedin.com/jobs/view/4272009471,Pending,2025-07-25 12:39:02.733159,2025-07-25 17:39:02.861286,Required experience is high,"
About the job
We are looking for a passionate and skilled iOS Developer (SDE II) to join our mobile engineering team. You will play a critical role in building and scaling high-performance iOS applications used by millions of users. The ideal candidate is deeply familiar with the iOS ecosystem, understands architectural best practices, and is capable of writing clean, maintainable, and testable code.

Key Responsibilities:
Design, build, and maintain advanced iOS applications using Swift and Objective-C.
Translate product requirements into scalable technical solutions.
Collaborate with cross-functional teams including product, design, and backend teams to define, design, and ship new features.
Ensure the performance, quality, and responsiveness of applications.
Identify and fix bugs, bottlenecks, and performance issues.
Maintain code quality through unit tests, code reviews, and CI/CD best practices.
Stay up to date with the latest iOS development trends, tools, and technologies.
Required Skills & Qualifications:
3–6 years of hands-on experience in iOS development.
Strong command over Swift, Objective-C, Xcode, and the iOS SDK.
Experience with MVVM/MVC architecture, RESTful APIs, and third-party libraries (e.g., Alamofire, Realm).
Understanding of Apple’s design principles and interface guidelines.
Familiarity with unit testing and performance tuning.
Strong debugging and problem-solving skills.
Bachelor's degree in Computer Science or a related field.
Nice to Have:
Experience with SwiftUI, Combine, or CoreML.
Exposure to Agile methodologies and Git workflows.

Why Join Us:
Be part of a fast-moving tech team building impactful, user-friendly apps with modern development practices and a collaborative work culture.

Capillary is an Equal Opportunity Employer and will not discriminate against any applicant for employment on the basis of race, age, religion, sex, veterans, individuals with disabilities, sexual orientation, or gender identity.

Disclaimer:
It has been brought to our attention that there have recently been instances of fraudulent job offers, purporting to be from Capillary Technologies. The individuals or organizations sending these false employment offers may pose as a Capillary Technologies recruiter or representative and request personal information, purchasing of equipment or funds to further the recruitment process or offer paid training. Be advised that Capillary Technologies does not extend unsolicited employment offers. Furthermore, Capillary Technologies does not charge prospective employees with fees or make requests for funding as a part of the recruitment process.
We commit to an inclusive recruitment process and equality of opportunity for all our job applicants.

Experience required 6 > Current Experience 5. Skipping this job!
",Skipped,Not Available
4275551783,https://www.linkedin.com/jobs/view/4275551783,Pending,2025-07-25 11:39:07.147277,2025-07-25 17:39:07.328382,Found a Bad Word in About Job,"
About the job
Life on the team

Join a dynamic supportive team working together to solve strong technical challenges by building high-quality ServiceNow solutions. Established as one of the first ServiceNow partners in Europe and awarded the 2022 EMEA Elite Segment Partner of the Year, we have grown a team with strong expertise across every aspect of the Now Platform. Our customers look to us for advice, best practice, and well-designed implementations. As well as to solve enterprise-wide process challenges by bringing great user experiences. We are expanding our team of Senior Technical Consultants and are seeking individuals who want to grow with us and progress to become Technical Architects. Through real-world experience and participation in the Certified Master Architect and Certified Technical Architect programs, we offer abundant opportunities for our team members to develop their technical skills and gain client-facing experience.

What you’ll do

Delivering high quality ServiceNow implementations based on business process requirements.
Effective delivery of quality solutions using Computacenter's methodologies and ensuring adherence to coding and design standards, and generating technical documentation
Increasing application operating efficiency and adapting to new requirements, as necessary
Keep up to date with current and future market developments, technologies, product, and strategies.
Attend and present at customer meetings to ensure understanding of customer requirements and to assist with knowledge transfer.
Recording, qualification and questioning of customer requirements, even in complex projects and in the case of unclear customer requirements and standards.
Successful handover of technology to internal or customer support function
we’ll support you to Attain and retain ServiceNow Certifications and partner accreditations.

What you’ll need

8+ years of experience in the ServiceNow Application Development
Configure and customize ServiceNow applications and modules using scripting (JavaScript), workflow, and other development techniques. 
Proven experience in configuring and customizing the ServiceNow platform and Expertise on 4-5 applications ranging from FSM & CSM, SPM, ITAM, Now App Engine and Portals.
Ability to provide technical leadership and support to Business Process Consultants. 
Responsible for leading technical implementations, providing technical design lead and mentoring junior members of the team.
Hands on Skills and/or experience in Web Technologies (e.g., Javascript, SOAP/REST web services, XML & JSON, Angular.js, Seismic)
Product line (CIS) accreditation preferred – Certified or pursuing.
Understanding of Software Development Lifecycle experience in Agile projects
Open and friendly personality, with ability to be customer facing. 
Be self-managing and capable of working alone or as part of a team. 

Experience & Education:

BE / BTech/MCA in Computer Science or related disciple 
Strong knowledge of JavaScript, scripting, and web development.
Familiarity with ITSM processes and ITIL framework.

Certifications

ServiceNow Certified Application Developer
ServiceNow Certified Implementation Specialist - CSM, SPM, ITAM, ITBM and others

About us

With over 20,000 employees across the globe, we work at the heart of digitisation, advising organisations on IT strategy, implementing the most appropriate technology, and helping our customers to source, transform and manage their technology infrastructure in over 70 countries. We deliver digital technology to some of the world’s greatest organisations, driving digital transformation, and enabling people and their businesses. 

Learning and development

Our people are our strength which is why we offer leadership training, coaching, mentoring, professional development, and international opportunities. Whichever direction you choose to go in – whether it’s a well-trodden path or a completely new part of the business.

You belong.

We passionately believe in the power of diversity and inclusion. We celebrate our differences because we know a diverse workforce with different experiences and perspectives helps us win together. And to do that, you need to feel comfortable to bring your whole self to work – and you can only do that when you feel supported, valued, and have a sense of belonging which is what we strive to achieve. Your application is considered on its merits regardless of your age, disability, ethnicity, faith, gender identity or sexual orientation. All that matters to us is that you share our vision and our values, and that you bring the experience and skills we need. We are proud to be a Disability Confident Employer, we welcome applications from people with a disability – and guarantee to interview applicants who have a disability and meet the essential requirements for the job.

Contains bad word ""Technical Lead"". Skipping this job!
",Skipped,Not Available
**********,https://www.linkedin.com/jobs/view/**********,Pending,2025-07-23 17:39:11.232162,2025-07-25 17:39:11.375753,Found a Bad Word in About Job,"
About the job
Onsurity is a rapidly growing employee healthcare benefits platform that provides flexible and customised healthcare subscriptions for SMEs, start-ups, and enterprises. We believe that access to healthcare benefits shouldn’t be a luxury. It is this philosophy that has strengthened our commitment towards making healthcare affordable and accessible for all.
Our subscriptions include discounts on medicine orders, health checkups, fitness plans, free doctor teleconsultations, and insurance, among other benefits. We believe in inclusivity which means our plans are not limited to full-time employees. We also cover contractual workers, interns, freelancers, and consultants.
We encourage you to read more about us on www.onsurity.com. You can also find us on LinkedIn, Instagram, and YouTube.
Below are stories that define our journey and showcase our commitment to democratizing healthcare across the country.
Onsurity is providing healthcare membership to SMEs with as low as three employees
The Journey Of Startups: Journey Onsurity
Cricketer Anil Kumble backs Onsurity as strategic advisor
Onsurity partners with Gulf Oil to offer healthcare to 10,000 truckers
83% Indian Employees Unaware Of Employer-Provided Healthcare Benefits, Says Study
News: Onsurity secures $45M Series B round led by Creaegis — People Matters

We were also featured in the first season of Disney+ Hotstar's remarkable series, The Great Indian Disruptors.
Our strategic partner and investor, cricketing legend Anil Kumble, is actively involved in our mission to make healthcare more accessible. Anil Kumble recently graced us with his presence at Onsurity’s Bengaluru office and engaged with our employees. He is passionate about our mission and has played an instrumental role in our journey so far.
Recently, Dun & Bradstreet India acknowledged our mission and conferred us with the Dun & Bradstreet Start-up 50 Trailblazer 2023 award.

About the Role:

We are seeking a skilled React Native Developer to join our mobile development team. You will be responsible for building cross-platform mobile applications for iOS and Android using the React Native framework. As part of our tech team, you will collaborate with product managers, designers, and backend developers to deliver high-quality, scalable mobile solutions.

Key Responsibilities:

Develop and maintain cross-platform mobile applications using React Native.
Integrate mobile apps with RESTful APIs and third-party services.
Collaborate with designers to implement intuitive UI/UX designs.
Write clean, maintainable, and well-documented code.
Optimize app performance and troubleshoot issues.
Conduct code reviews and maintain code quality.
Stay up-to-date with emerging technologies and mobile development trends.

Requirements:

2+ years of experience with React Native and JavaScript/TypeScript.
Experience deploying apps to the Apple App Store and Google Play Store.
Solid understanding of mobile architecture, performance optimization, and native device features.
Familiarity with Redux, MobX, or other state management tools.
Experience integrating native modules (e.g., Swift, Objective-C, Java, Kotlin) is a plus.
Understanding of REST APIs, JSON, and offline storage mechanisms.
Familiarity with Git and version control tools.
Excellent problem-solving and communication skills.

Contains bad word ""Product Manager"". Skipping this job!
",Skipped,Not Available
4270223783,https://www.linkedin.com/jobs/view/4270223783,Pending,2025-07-21 17:39:17.719918,2025-07-25 17:39:17.829742,Found a Bad Word in About Job,"
About the job
Job Title: Fullstack Developer

Experience Required: 2–4 years

Location: Bengaluru (Hybrid) (3 days WFO, 2 days WFH)

Employment Type: Full-time

Key Responsibilities

Design, develop, test, and maintain scalable fullstack applications using modern technologies.
Implement responsive and cross-browser compatible UI components with HTML, CSS (SASS/LESS), and JavaScript.
Develop robust backend services and APIs using Node.js, Express.js, JavaScript/TypeScript.
Collaborate with cross-functional teams including designers, product managers, and other developers.
Ensure code quality through best practices, including unit testing and code reviews.
Contribute to architectural decisions and provide innovative solutions to complex challenges.

Must-Have Qualifications

2–3 years of professional experience as a Fullstack Engineer.
Strong proficiency in HTML, CSS (LESS/SASS), and JavaScript with a deep understanding of responsive and cross-browser web design principles.
Hands-on experience with modern front-end frameworks such as React or Angular.
Solid backend development experience in Node.js, Express.js, and either JavaScript or TypeScript.
Strong grasp of software engineering fundamentals, including data structures, algorithms, and problem-solving skills.

Good-to-Have Skills

Experience building applications that integrate AI tools or AI-driven workflows.
Exposure to backend development using Python or Java.
Knowledge of databases, including MongoDB, DynamoDB, MySQL, Redis, ElastiCache, and ElasticSearchDB.
Experience designing and developing RESTful APIs, preferably with metric-driven API Gateway integrations.
Familiarity with AWS services, Kubernetes, microservices, and domain-driven architecture.
Excellent written and verbal communication skills, with the ability to clearly present technical concepts to stakeholders and team members.

Skills: angular,typescript,html,node.js,express.js,javascript,react,css,restful apis,css (sass/less)

Contains bad word ""Product Manager"". Skipping this job!
",Skipped,Not Available
4275556779,https://www.linkedin.com/jobs/view/4275556779,Pending,2025-07-25 12:39:46.057799,2025-07-25 17:39:46.249796,Found a Bad Word in About Job,"
About the job
Job Title : C/C++ Developer
Location State : Karnataka
Location City : Bangalore
Experience Required : 2+ Year(s)
Shift: General
Work Mode: Hybrid
Position Type: Contract

Company Name: VARITE INDIA PRIVATE LIMITED

About The Client: An American multinational digital communications technology conglomerate corporation headquartered in San Jose, California. The Client develops, manufactures, and sells networking hardware, software, telecommunications equipment, and other high-technology services and products. The Client specializes in specific tech markets, such as the Internet of Things (IoT), domain security, videoconferencing, and energy management. It is one of the largest technology companies in the world, ranking 82nd on the Fortune 100 with over $51 billion in revenue and nearly 83,300 employees.

Essential Job Functions: 
Work on Linux-based platforms and understand open-source processes. 
Solve complex problems using strong troubleshooting skills. 
Communicate and collaborate effectively, both verbally and in writing. 
Handle ambiguity and prioritize tasks effectively. 
Define problems, analyze facts, and develop logical solutions. 
Foster teamwork and resolve issues positively. 

Qualifications: 
Experience Range: 2 to 3 years 
 Skills Required: 
Programming Languages: C/C++. 
Platform: Linux 
Core Concepts: Multithreading, Singleton, Algorithms, Data Structures, Object-Oriented Design, and Database concepts. 
Asynchronous Transactions: REST API, Event-driven patterns, IPC, and HTTP transactions. 
Security: TLS, mTLS, certificate management, and ciphers. 
Networking Fundamentals: IP, TCP/UDP, DNS, HTTP. 
Troubleshooting: Debugging functional, scale, and threading issues in Linux OS. 
Agile Experience: Familiarity with Agile development environments.

How to Apply: Interested candidates are invited to submit their resume using the apply online button on this job post.

Equal Opportunity Employer: VARITE is an equal opportunity employer. We celebrate diversity and are committed to creating an inclusive environment for all employees. We do not discriminate on the basis of race, color, religion, sex, sexual orientation, gender identity or expression, national origin, age, marital status, veteran status, or disability status.

Unlock Rewards: Refer Candidates and Earn. If you're not available or interested in this opportunity, please pass this along to anyone in your network who might be a good fit and interested in our open positions. VARITE offers a Candidate Referral program, where you'll receive a one-time referral bonus based on the following scale if the referred candidate completes a three-month assignment with VARITE.

Exp Req - Referral Bonus
0 - 2 Yrs. - INR 5,000
2 - 6 Yrs. - INR 7,500
6 + Yrs. - INR 10,000

About VARITE: VARITE is a global staffing and IT consulting company providing technical consulting and team augmentation services to Fortune 500 Companies in USA, UK, CANADA and INDIA. VARITE is currently a primary and direct vendor to the leading corporations in the verticals of Networking, Cloud Infrastructure, Hardware and Software, Digital Marketing and Media Solutions, Clinical Diagnostics, Utilities, Gaming and Entertainment, and Financial Services.

Contains bad word ""Marketing"". Skipping this job!
",Skipped,Not Available
4272017952,https://www.linkedin.com/jobs/view/4272017952,Pending,2025-07-25 16:40:21.129419,2025-07-25 17:40:21.261747,Found a Bad Word in About Job,"
About the job
Type: Contract (C2H)
Duration: 6 Months 
Location: Bangalore (Hybrid 3 Days )
Rate: 18 LPA
Experience: 6 to 9 Years

Looking for an experienced Hyperion support Developer with 5-8 years of relevant experience. Candidates with financial background is preferred. The detailed responsibilities are mentioned below.
HFM, Planning, Essbase, HPCM
Perform coding and configuration to enhance and maintain Oracle EPM tools or Hyperion applications, including Planning, Essbase (BSO and ASO cubes), FDMEE,HPCM.
Monitor, maintain security, management process controls, task flows, business rules, scripts, member lists, journal module, objects (Webforms, Grids, Task lists), consolidation and data clearing procedures, metadata updates etc.
Strong knowledge of Essbase scripting and Calc Manager Rules, Essbase Calc Scripts, Batch scripts, MDX & MAXL. EPM solution, Essbase Cubes.
Work closely with Master data team and finance teams to manage metadata changes, business rule updates, form/dashboard enhancements, and data loads.
Have good general functional knowledge and understanding of budgeting, forecasting and financial analysis and close processes
Sound knowledge of Life Cycle Management, user Management, Security Management etc
Provide level one and level two support in line with the team’s remit: CoA validations and deployments using EPMA / DRM; User support and setups.
Good analytical, problem solving, & communication skills
Have experience working with ServiceNow ticketing tool, approval process and SLA.
Address user queries with webforms, SmartView, data reconciliation in HFM
 Read, understand and update HFM rules per user requirements
 Update HFM application metadata and security as required
 Re-shell application and reconcile data for regression testing before monthly updates.
 Deploy updates to Pre-Prod and reconcile data before monthly updates in HFM and Planning/Essbase apps.
 Deploy changes to Production after preproduction COA and new enhancements testing.
 FDMEE: Monitor data loading schedule jobs and if any failures/kickouts. Address these by liaising with entity owners (in the case of data quality errors) or updating dimension maps

Contains bad word ""Finance"". Skipping this job!
",Skipped,Not Available
4270239677,https://www.linkedin.com/jobs/view/4270239677,Pending,2025-07-21 17:41:09.700350,2025-07-25 17:41:09.988367,Found a Bad Word in About Job,"
About the job
Position - Frontend - Mobile RN Developer

Experience - 5+ Years

Location - Multiple locations across India. Here are some of the key locations:
Bangalore
Bhubaneswar
Chennai
Coimbatore
Gandhinagar
Gurugram
Hyderabad
Kolkata
Mumbai
Noida
Pune
Salem
Tiruchirappalli

Must Have Skills - Native module , Axios , Custom hooks , Husky , SonarQube , Redux , info.plist , manisfest.xml 

Must Have:
Expertise in strategizing and developing mobile applications for both iOS and Android platforms using React Native.
5+ years of hands-on experience in building and designing mobile applications with React Native - TypeScript.
Strong proficiency with Redux-Saga for managing application state and handling side effects.
Solid knowledge of JavaScript, TypeScript, Swift, and Kotlin for cross-platform and native mobile development.
Experience integrating third-party libraries into React Native apps, such as Firebase, Sentry, and others for push notifications, analytics, and crash reporting.
Ability to build and design reusable NPM packages for multiple projects, promoting efficient code sharing.
Proven experience developing custom native modules for at least one platform (iOS with Swift/Obj-C or Androidwith Java/Kotlin).
Proficient in creating React Native components that are efficient, maintainable, and easy to test.
Strong knowledge of unit testing and writing test cases using Jest to ensure high code quality.
Version control using Git to maintain a clean and organized codebase.
Experience working with design systems such as Atomic Design or Fabric to maintain consistency across applications.
Familiarity with Figma to translate design specs into well-crafted, functional mobile interfaces.
Comfortable using collaboration tools like JIRA, Confluence, and other project management software to track progress and communicate effectively within teams.

About CLPS RiDiK
RiDiK is a global technology solutions provider and a subsidiary of CLPS Incorporation (NASDAQ: CLPS), delivering cutting-edge end-to-end services across banking, wealth management, and e-commerce. With deep expertise in AI, cloud, big data, and blockchain, we support clients across Asia, North America, and the Middle East in driving digital transformation and achieving sustainable growth. Operating from regional hubs in 10 countries and backed by a global delivery network, we combine local insight with technical excellence to deliver real, measurable impact. Join RiDiK and be part of an innovative, fast-growing team shaping the future of technology across industries.

Contains bad word ""Blockchain"". Skipping this job!
",Skipped,Not Available
**********,https://www.linkedin.com/jobs/view/**********,Pending,2025-07-25 16:43:28.198018,2025-07-25 17:43:28.433148,Found a Bad Word in About Job,"
About the job
About Us
MyRemoteTeam, Inc is a fast-growing distributed workforce enabler, helping companies scale with top global talent. We empower businesses by providing world-class software engineers, operations support, and infrastructure to help them grow faster and better.

Job Title: Full Stack Developer – Java + React
Experience: 5+ Years

Job Summary:
We are looking for a talented and experienced Full Stack Developer (Java + React) with a strong background in building scalable, high-quality, and high-performance web applications. The ideal candidate will have hands-on experience in Java, Spring Boot, Microservices architecture, and React.js.

Key Responsibilities:
Design, develop, and maintain scalable backend services using Java, Spring Boot, and Microservices.
Develop intuitive and responsive frontend interfaces using React.js.
Collaborate with product managers, UI/UX designers, and other developers to understand requirements and translate them into technical solutions.
Ensure code quality through unit testing, code reviews, and best practices.
Integrate APIs and work with third-party services as needed.
Optimize application performance and scalability.
Participate in Agile/Scrum development cycles, including sprint planning and daily stand-ups.
Troubleshoot and resolve production issues in a timely manner.

Required Skills:
5+ years of experience in Java development
Strong experience with Spring Boot and building RESTful APIs
Solid understanding of Microservices architecture
2+ years of hands-on experience with React.js, JavaScript, HTML5, and CSS3
Experience working with databases like MySQL, PostgreSQL, or MongoDB
Familiarity with version control systems (Git)
Understanding of CI/CD pipelines and deployment processes
Good problem-solving skills and attention to detail

Contains bad word ""Product Manager"". Skipping this job!
",Skipped,Not Available
4275596055,https://www.linkedin.com/jobs/view/4275596055,Pending,2025-07-25 16:43:35.231568,2025-07-25 17:43:35.487535,Required experience is high,"
About the job
We have a Walk-In Drive for Java Full Stack Developer requirement on 28th&29th July 25.

Drive venue:
Address: Adobe Tower, Block A, Prestige Platina Tech Park, Marathahalli-Sarjapur Outer Ring Road, Kadubeesanahalli



Java Full Stack Developer: 

Core Java with Microservices - 40%
Cloud(Azure/AWS) - 20%
Rest API integration - 10%
Angular/ReactJS - 10%
SQL - 10%
SpringBoot - 10%


Location : Bengaluru - Hybrid
NP : 30days
Experience :6 to 10 yrs
Budget : Max 30 LPA
Payroll : STL - Sterlite Technologies Limited

JD :
Job Title : Java Full Stack Developer
Work Location : Bengaluru
Experience : 6-8 Yrs.
Job Mode : Hybrid
Responsibilities:
Candidate must have hands-on implementation experience with Java.
Candidate will need to work on Design and Development of business-critical solutions using cloud-based services on AWS/Azure.
Candidate will need to work closely with other teams in IT including Business Solution Analyst, QA, Release Management, and Operations.
Candidate will be responsible for collaborating with other engineers and project stakeholders on the design and development of solutions and integrations.
Candidate will be responsible for building and unit testing the integrations using the appropriate technology. They will work closely with the testing team to validate their integration, and with Operations to investigate, troubleshoot, and address production issues.
The candidate should have a minimum of 6+ years of overall IT experience with at least last 3+ years on Java development and frameworks mainly Spring.
Experience developing and managing RESTful API applications using microservices.
Demonstrable Experience in developing scalable Cloud based services on AWS/Azure.
Experience in Javascript, React would be an added advantage.
Experience in SQL and database concepts.
Proficiency in data structures and algorithms.
Experience troubleshooting and finding multiple solutions to a complex problem.
Excellent communication skills and demonstrated ability to effectively communicate technical issues and resolve problems.
Experience in delivering IT solutions using Agile (Scrum) project methodology and practices.

Experience required 6 > Current Experience 5. Skipping this job!
",Skipped,Not Available
4275590038,https://www.linkedin.com/jobs/view/4275590038,Pending,2025-07-25 15:43:39.181459,2025-07-25 17:43:39.478546,Found a Bad Word in About Job,"
About the job
Job Title: Senior Software Architect
Location: India (Remote/Hybrid)
Experience: 5–8 years
Education: BS/MS in Computer Science or equivalent

About the Role
We’re seeking a high-impact Senior Software Architect to design, lead, and deliver core product features and scalable systems. In this role, you will mentor engineers, drive technical excellence, and own the full software development lifecycle — from design to deployment.

Key Responsibilities
Architect and design scalable, distributed systems for high-performance cloud environments.
Lead critical product features and core infrastructure projects.
Mentor and guide engineers through design/code reviews and best practices.
Own end-to-end SDLC: from requirements gathering to design, development, testing, deployment, and support.
Collaborate across global teams to deliver innovative and reliable software solutions.
Continuously improve system performance, scalability, and resilience.

Technical Expertise
Strong background in Java, Big Data, and Distributed Systems.
Proficient in Microservices Architecture and Cloud platforms (AWS, Azure).
Deep knowledge of Algorithms, Data Structures, and system design principles.
Hands-on experience with NoSQL databases, Hadoop ecosystem (HDFS, Hive, etc.).

What We’re Looking For
5–8 years of experience in software engineering, including system architecture and cloud-based design.
A proven track record of driving complex projects from concept to production.
Passion for mentoring, technical leadership, and continuous improvement.
Excellent communication and collaboration skills in a fast-paced, global team environment.

Contains bad word ""Technical Lead"". Skipping this job!
",Skipped,Not Available
4272031343,https://www.linkedin.com/jobs/view/4272031343,Pending,2025-07-25 15:43:43.225487,2025-07-25 17:43:43.387146,Found a Bad Word in About Job,"
About the job
Senior Software Engineer - Backend
FinacPlus provide virtual business process services to overseas clients and this position is to be part of the dedicated team which providesmortgage back-office servicesand software productdevelopment to Toorak Capital Partners https://www.toorakcapital.com/ from Mortgage FinanceIndustry.

About Toorak Capital

Toorak Capital Partnersis an integrated lending platformthat funds small business purposeresidential, multifamily and mixed-use loans throughout the U.S. and the United Kingdom.

Headquartered in Summit, N.J., Toorak Capital Partners acquires and manages loans directly from private lendersthat originate high credit quality loans.

RESPONSIBILITIES
To Work as Backend Developerin developing Cloud Based Web Applications
To be part of team working on various types of web applications related to MortgageFinance.
Experience in solving a real-world problem of Implementing, Designing and helping develop a new Enterprise class Product from ground-up.
You have expertise in the AWS Cloud Infrastructure and Micro-services architecture around the AWS Service stack like Lambdas, SQS, SNS, MySQL Databases along with Dockers and containerized solutions/applications.
Experienced in Relational and No-SQL databases and scalable design.
Experience in solvingchallenging problems by developing elegant,maintainable code.
Delivered rapid iterations of software based on user feedback and metrics.
Help the team make key decisions on our productand technology direction.
You actively contribute to the adoptionof frameworks, standards, and new technologies.

QUALIFICATIONS AND EXPERIENCE
Overall 5 to 7 years of experience. Node.js experience is must.
At least 3+ years of experience or couple of large-scale productsdelivered on microservices.
Strong design skillson microservices and AWS platforminfrastructure.
Excellent programming skill in Python,Node.js and Java script.
Hands on development in rest API’s.
Good understanding of nuances of distributed systems,scalability, and availability.

Location: Bangalore
Timings: 11am To 8pm IST
Salary Range: Best in Industry
Send Application To: <EMAIL>

Contains bad word ""Senior Software Engineer"". Skipping this job!
",Skipped,Not Available
**********,https://www.linkedin.com/jobs/view/**********,Pending,2025-07-25 16:43:46.403233,2025-07-25 17:43:46.609635,Found a Bad Word in About Job,"
About the job
About Us
MyRemoteTeam, Inc is a fast-growing distributed workforce enabler, helping companies scale with top global talent. We empower businesses by providing world-class software engineers, operations support, and infrastructure to help them grow faster and better.

Job Title: Full Stack Developer – Java + React
Experience: 5+ Years

Job Summary:
We are looking for a talented and experienced Full Stack Developer (Java + React) with a strong background in building scalable, high-quality, and high-performance web applications. The ideal candidate will have hands-on experience in Java, Spring Boot, Microservices architecture, and React.js.

Key Responsibilities:
Design, develop, and maintain scalable backend services using Java, Spring Boot, and Microservices.
Develop intuitive and responsive frontend interfaces using React.js.
Collaborate with product managers, UI/UX designers, and other developers to understand requirements and translate them into technical solutions.
Ensure code quality through unit testing, code reviews, and best practices.
Integrate APIs and work with third-party services as needed.
Optimize application performance and scalability.
Participate in Agile/Scrum development cycles, including sprint planning and daily stand-ups.
Troubleshoot and resolve production issues in a timely manner.

Required Skills:
5+ years of experience in Java development
Strong experience with Spring Boot and building RESTful APIs
Solid understanding of Microservices architecture
2+ years of hands-on experience with React.js, JavaScript, HTML5, and CSS3
Experience working with databases like MySQL, PostgreSQL, or MongoDB
Familiarity with version control systems (Git)
Understanding of CI/CD pipelines and deployment processes
Good problem-solving skills and attention to detail

Contains bad word ""Product Manager"". Skipping this job!
",Skipped,Not Available
4275582302,https://www.linkedin.com/jobs/view/4275582302,Pending,2025-07-25 14:43:47.319210,2025-07-25 17:43:47.548605,Found a Bad Word in About Job,"
About the job
Job Title : C++ Developer
Location State : Karnataka
Location City : Bangalore
Experience Required : 2+ Year(s)
Shift: General
Work Mode: Hybrid
Position Type: Contract

Company Name: VARITE INDIA PRIVATE LIMITED

About The Client: An American multinational digital communications technology conglomerate corporation headquartered in San Jose, California. The Client develops, manufactures, and sells networking hardware, software, telecommunications equipment, and other high-technology services and products. The Client specializes in specific tech markets, such as the Internet of Things (IoT), domain security, videoconferencing, and energy management. It is one of the largest technology companies in the world, ranking 82nd on the Fortune 100 with over $51 billion in revenue and nearly 83,300 employees.

Essential Job Functions:
Work on Linux-based platforms and understand open-source processes.
Solve complex problems using strong troubleshooting skills.
Communicate and collaborate effectively, both verbally and in writing.
Handle ambiguity and prioritize tasks effectively.
Define problems, analyze facts, and develop logical solutions.
Foster teamwork and resolve issues positively.

Qualifications:
Experience Range: 2 to 3 years

Skills Required:
Programming Languages: C/C++.
Platform: Linux
Core Concepts: Multithreading, Singleton, Algorithms, Data Structures, Object-Oriented Design, and Database concepts.
Asynchronous Transactions: REST API, Event-driven patterns, IPC, and HTTP transactions.
Security: TLS, mTLS, certificate management, and ciphers.
Networking Fundamentals: IP, TCP/UDP, DNS, HTTP.
Troubleshooting: Debugging functional, scale, and threading issues in Linux OS.
Agile Experience: Familiarity with Agile development environments.

How to Apply: Interested candidates are invited to submit their resume using the apply online button on this job post.

Equal Opportunity Employer: VARITE is an equal opportunity employer. We celebrate diversity and are committed to creating an inclusive environment for all employees. We do not discriminate on the basis of race, color, religion, sex, sexual orientation, gender identity or expression, national origin, age, marital status, veteran status, or disability status.

Unlock Rewards: Refer Candidates and Earn. If you're not available or interested in this opportunity, please pass this along to anyone in your network who might be a good fit and interested in our open positions. VARITE offers a Candidate Referral program, where you'll receive a one-time referral bonus based on the following scale if the referred candidate completes a three-month assignment with VARITE.

Exp Req - Referral Bonus
0 - 2 Yrs. - INR 5,000
2 - 6 Yrs. - INR 7,500
6 + Yrs. - INR 10,000

About VARITE: VARITE is a global staffing and IT consulting company providing technical consulting and team augmentation services to Fortune 500 Companies in USA, UK, CANADA and INDIA. VARITE is currently a primary and direct vendor to the leading corporations in the verticals of Networking, Cloud Infrastructure, Hardware and Software, Digital Marketing and Media Solutions, Clinical Diagnostics, Utilities, Gaming and Entertainment, and Financial Services.

Contains bad word ""Marketing"". Skipping this job!
",Skipped,Not Available
4275596055,https://www.linkedin.com/jobs/view/4275596055,Pending,2025-07-25 16:43:52.873952,2025-07-25 17:43:53.211550,Required experience is high,"
About the job
We have a Walk-In Drive for Java Full Stack Developer requirement on 28th&29th July 25.

Drive venue:
Address: Adobe Tower, Block A, Prestige Platina Tech Park, Marathahalli-Sarjapur Outer Ring Road, Kadubeesanahalli



Java Full Stack Developer: 

Core Java with Microservices - 40%
Cloud(Azure/AWS) - 20%
Rest API integration - 10%
Angular/ReactJS - 10%
SQL - 10%
SpringBoot - 10%


Location : Bengaluru - Hybrid
NP : 30days
Experience :6 to 10 yrs
Budget : Max 30 LPA
Payroll : STL - Sterlite Technologies Limited

JD :
Job Title : Java Full Stack Developer
Work Location : Bengaluru
Experience : 6-8 Yrs.
Job Mode : Hybrid
Responsibilities:
Candidate must have hands-on implementation experience with Java.
Candidate will need to work on Design and Development of business-critical solutions using cloud-based services on AWS/Azure.
Candidate will need to work closely with other teams in IT including Business Solution Analyst, QA, Release Management, and Operations.
Candidate will be responsible for collaborating with other engineers and project stakeholders on the design and development of solutions and integrations.
Candidate will be responsible for building and unit testing the integrations using the appropriate technology. They will work closely with the testing team to validate their integration, and with Operations to investigate, troubleshoot, and address production issues.
The candidate should have a minimum of 6+ years of overall IT experience with at least last 3+ years on Java development and frameworks mainly Spring.
Experience developing and managing RESTful API applications using microservices.
Demonstrable Experience in developing scalable Cloud based services on AWS/Azure.
Experience in Javascript, React would be an added advantage.
Experience in SQL and database concepts.
Proficiency in data structures and algorithms.
Experience troubleshooting and finding multiple solutions to a complex problem.
Excellent communication skills and demonstrated ability to effectively communicate technical issues and resolve problems.
Experience in delivering IT solutions using Agile (Scrum) project methodology and practices.

Experience required 6 > Current Experience 5. Skipping this job!
",Skipped,Not Available
4270157274,https://www.linkedin.com/jobs/view/4270157274,Pending,2025-07-25 13:43:53.432500,2025-07-25 17:43:53.677570,Found a Bad Word in About Job,"
About the job
Job description

We are looking for an experienced Android Developer to join our dynamic team. The ideal candidate will be responsible for designing, developing, and deploying Android applications to deliver innovative and intelligent user experiences.

Key Responsibilities:

Develop and maintain Android applications using Android Studio.
Support and debug cross-platform applications with Xcode (for iOS builds).
Collaborate with designers and product managers to create intuitive, user-friendly mobile experiences.
Integrate third-party APIs and SDKs.
Ensure performance, quality, and responsiveness of applications.
Write clean, maintainable code following best practices (MVVM, Clean Architecture, etc.).
Manage code using version control systems like Git.
Conduct testing and debugging across multiple device types and OS versions.
Participate in code reviews and contribute to team knowledge sharing.

Qualifications:

Bachelors or Masters degree in Computer Science, Engineering, or a related field.
2 years of experience as Andriod Developer
Experience in Android app development using Kotlin and/or Java,
Proficient in integrating RESTful APIs and third-party SDKs.
Strong understanding of Android UI/UX principles, design patterns (MVVM, MVP, etc.)
Proficiency in Android Studio.
Familiarity with Xcode and the iOS build/debug process.
Understanding of mobile UI/UX principles and best practices.
Experience consuming RESTful APIs and working with JSON.
Familiar with version control tools such as Git.
Experience with modern architecture patterns (MVVM, Clean Architecture).

Contains bad word ""Product Manager"". Skipping this job!
",Skipped,Not Available
4275590038,https://www.linkedin.com/jobs/view/4275590038,Pending,2025-07-25 15:43:57.114966,2025-07-25 17:43:57.274589,Found a Bad Word in About Job,"
About the job
Job Title: Senior Software Architect
Location: India (Remote/Hybrid)
Experience: 5–8 years
Education: BS/MS in Computer Science or equivalent

About the Role
We’re seeking a high-impact Senior Software Architect to design, lead, and deliver core product features and scalable systems. In this role, you will mentor engineers, drive technical excellence, and own the full software development lifecycle — from design to deployment.

Key Responsibilities
Architect and design scalable, distributed systems for high-performance cloud environments.
Lead critical product features and core infrastructure projects.
Mentor and guide engineers through design/code reviews and best practices.
Own end-to-end SDLC: from requirements gathering to design, development, testing, deployment, and support.
Collaborate across global teams to deliver innovative and reliable software solutions.
Continuously improve system performance, scalability, and resilience.

Technical Expertise
Strong background in Java, Big Data, and Distributed Systems.
Proficient in Microservices Architecture and Cloud platforms (AWS, Azure).
Deep knowledge of Algorithms, Data Structures, and system design principles.
Hands-on experience with NoSQL databases, Hadoop ecosystem (HDFS, Hive, etc.).

What We’re Looking For
5–8 years of experience in software engineering, including system architecture and cloud-based design.
A proven track record of driving complex projects from concept to production.
Passion for mentoring, technical leadership, and continuous improvement.
Excellent communication and collaboration skills in a fast-paced, global team environment.

Contains bad word ""Technical Lead"". Skipping this job!
",Skipped,Not Available
4272031343,https://www.linkedin.com/jobs/view/4272031343,Pending,2025-07-25 15:44:00.900488,2025-07-25 17:44:01.032745,Found a Bad Word in About Job,"
About the job
Senior Software Engineer - Backend
FinacPlus provide virtual business process services to overseas clients and this position is to be part of the dedicated team which providesmortgage back-office servicesand software productdevelopment to Toorak Capital Partners https://www.toorakcapital.com/ from Mortgage FinanceIndustry.

About Toorak Capital

Toorak Capital Partnersis an integrated lending platformthat funds small business purposeresidential, multifamily and mixed-use loans throughout the U.S. and the United Kingdom.

Headquartered in Summit, N.J., Toorak Capital Partners acquires and manages loans directly from private lendersthat originate high credit quality loans.

RESPONSIBILITIES
To Work as Backend Developerin developing Cloud Based Web Applications
To be part of team working on various types of web applications related to MortgageFinance.
Experience in solving a real-world problem of Implementing, Designing and helping develop a new Enterprise class Product from ground-up.
You have expertise in the AWS Cloud Infrastructure and Micro-services architecture around the AWS Service stack like Lambdas, SQS, SNS, MySQL Databases along with Dockers and containerized solutions/applications.
Experienced in Relational and No-SQL databases and scalable design.
Experience in solvingchallenging problems by developing elegant,maintainable code.
Delivered rapid iterations of software based on user feedback and metrics.
Help the team make key decisions on our productand technology direction.
You actively contribute to the adoptionof frameworks, standards, and new technologies.

QUALIFICATIONS AND EXPERIENCE
Overall 5 to 7 years of experience. Node.js experience is must.
At least 3+ years of experience or couple of large-scale productsdelivered on microservices.
Strong design skillson microservices and AWS platforminfrastructure.
Excellent programming skill in Python,Node.js and Java script.
Hands on development in rest API’s.
Good understanding of nuances of distributed systems,scalability, and availability.

Location: Bangalore
Timings: 11am To 8pm IST
Salary Range: Best in Industry
Send Application To: <EMAIL>

Contains bad word ""Senior Software Engineer"". Skipping this job!
",Skipped,Not Available
4275582302,https://www.linkedin.com/jobs/view/4275582302,Pending,2025-07-25 14:44:04.897675,2025-07-25 17:44:05.070065,Found a Bad Word in About Job,"
About the job
Job Title : C++ Developer
Location State : Karnataka
Location City : Bangalore
Experience Required : 2+ Year(s)
Shift: General
Work Mode: Hybrid
Position Type: Contract

Company Name: VARITE INDIA PRIVATE LIMITED

About The Client: An American multinational digital communications technology conglomerate corporation headquartered in San Jose, California. The Client develops, manufactures, and sells networking hardware, software, telecommunications equipment, and other high-technology services and products. The Client specializes in specific tech markets, such as the Internet of Things (IoT), domain security, videoconferencing, and energy management. It is one of the largest technology companies in the world, ranking 82nd on the Fortune 100 with over $51 billion in revenue and nearly 83,300 employees.

Essential Job Functions:
Work on Linux-based platforms and understand open-source processes.
Solve complex problems using strong troubleshooting skills.
Communicate and collaborate effectively, both verbally and in writing.
Handle ambiguity and prioritize tasks effectively.
Define problems, analyze facts, and develop logical solutions.
Foster teamwork and resolve issues positively.

Qualifications:
Experience Range: 2 to 3 years

Skills Required:
Programming Languages: C/C++.
Platform: Linux
Core Concepts: Multithreading, Singleton, Algorithms, Data Structures, Object-Oriented Design, and Database concepts.
Asynchronous Transactions: REST API, Event-driven patterns, IPC, and HTTP transactions.
Security: TLS, mTLS, certificate management, and ciphers.
Networking Fundamentals: IP, TCP/UDP, DNS, HTTP.
Troubleshooting: Debugging functional, scale, and threading issues in Linux OS.
Agile Experience: Familiarity with Agile development environments.

How to Apply: Interested candidates are invited to submit their resume using the apply online button on this job post.

Equal Opportunity Employer: VARITE is an equal opportunity employer. We celebrate diversity and are committed to creating an inclusive environment for all employees. We do not discriminate on the basis of race, color, religion, sex, sexual orientation, gender identity or expression, national origin, age, marital status, veteran status, or disability status.

Unlock Rewards: Refer Candidates and Earn. If you're not available or interested in this opportunity, please pass this along to anyone in your network who might be a good fit and interested in our open positions. VARITE offers a Candidate Referral program, where you'll receive a one-time referral bonus based on the following scale if the referred candidate completes a three-month assignment with VARITE.

Exp Req - Referral Bonus
0 - 2 Yrs. - INR 5,000
2 - 6 Yrs. - INR 7,500
6 + Yrs. - INR 10,000

About VARITE: VARITE is a global staffing and IT consulting company providing technical consulting and team augmentation services to Fortune 500 Companies in USA, UK, CANADA and INDIA. VARITE is currently a primary and direct vendor to the leading corporations in the verticals of Networking, Cloud Infrastructure, Hardware and Software, Digital Marketing and Media Solutions, Clinical Diagnostics, Utilities, Gaming and Entertainment, and Financial Services.

Contains bad word ""Marketing"". Skipping this job!
",Skipped,Not Available
4270157274,https://www.linkedin.com/jobs/view/4270157274,Pending,2025-07-25 13:44:11.089985,2025-07-25 17:44:11.343557,Found a Bad Word in About Job,"
About the job
Job description

We are looking for an experienced Android Developer to join our dynamic team. The ideal candidate will be responsible for designing, developing, and deploying Android applications to deliver innovative and intelligent user experiences.

Key Responsibilities:

Develop and maintain Android applications using Android Studio.
Support and debug cross-platform applications with Xcode (for iOS builds).
Collaborate with designers and product managers to create intuitive, user-friendly mobile experiences.
Integrate third-party APIs and SDKs.
Ensure performance, quality, and responsiveness of applications.
Write clean, maintainable code following best practices (MVVM, Clean Architecture, etc.).
Manage code using version control systems like Git.
Conduct testing and debugging across multiple device types and OS versions.
Participate in code reviews and contribute to team knowledge sharing.

Qualifications:

Bachelors or Masters degree in Computer Science, Engineering, or a related field.
2 years of experience as Andriod Developer
Experience in Android app development using Kotlin and/or Java,
Proficient in integrating RESTful APIs and third-party SDKs.
Strong understanding of Android UI/UX principles, design patterns (MVVM, MVP, etc.)
Proficiency in Android Studio.
Familiarity with Xcode and the iOS build/debug process.
Understanding of mobile UI/UX principles and best practices.
Experience consuming RESTful APIs and working with JSON.
Familiar with version control tools such as Git.
Experience with modern architecture patterns (MVVM, Clean Architecture).

Contains bad word ""Product Manager"". Skipping this job!
",Skipped,Not Available
4275551783,https://www.linkedin.com/jobs/view/4275551783,Pending,2025-07-25 11:44:42.382167,2025-07-25 17:44:42.662300,Found a Bad Word in About Job,"
About the job
Life on the team

Join a dynamic supportive team working together to solve strong technical challenges by building high-quality ServiceNow solutions. Established as one of the first ServiceNow partners in Europe and awarded the 2022 EMEA Elite Segment Partner of the Year, we have grown a team with strong expertise across every aspect of the Now Platform. Our customers look to us for advice, best practice, and well-designed implementations. As well as to solve enterprise-wide process challenges by bringing great user experiences. We are expanding our team of Senior Technical Consultants and are seeking individuals who want to grow with us and progress to become Technical Architects. Through real-world experience and participation in the Certified Master Architect and Certified Technical Architect programs, we offer abundant opportunities for our team members to develop their technical skills and gain client-facing experience.

What you’ll do

Delivering high quality ServiceNow implementations based on business process requirements.
Effective delivery of quality solutions using Computacenter's methodologies and ensuring adherence to coding and design standards, and generating technical documentation
Increasing application operating efficiency and adapting to new requirements, as necessary
Keep up to date with current and future market developments, technologies, product, and strategies.
Attend and present at customer meetings to ensure understanding of customer requirements and to assist with knowledge transfer.
Recording, qualification and questioning of customer requirements, even in complex projects and in the case of unclear customer requirements and standards.
Successful handover of technology to internal or customer support function
we’ll support you to Attain and retain ServiceNow Certifications and partner accreditations.

What you’ll need

8+ years of experience in the ServiceNow Application Development
Configure and customize ServiceNow applications and modules using scripting (JavaScript), workflow, and other development techniques. 
Proven experience in configuring and customizing the ServiceNow platform and Expertise on 4-5 applications ranging from FSM & CSM, SPM, ITAM, Now App Engine and Portals.
Ability to provide technical leadership and support to Business Process Consultants. 
Responsible for leading technical implementations, providing technical design lead and mentoring junior members of the team.
Hands on Skills and/or experience in Web Technologies (e.g., Javascript, SOAP/REST web services, XML & JSON, Angular.js, Seismic)
Product line (CIS) accreditation preferred – Certified or pursuing.
Understanding of Software Development Lifecycle experience in Agile projects
Open and friendly personality, with ability to be customer facing. 
Be self-managing and capable of working alone or as part of a team. 

Experience & Education:

BE / BTech/MCA in Computer Science or related disciple 
Strong knowledge of JavaScript, scripting, and web development.
Familiarity with ITSM processes and ITIL framework.

Certifications

ServiceNow Certified Application Developer
ServiceNow Certified Implementation Specialist - CSM, SPM, ITAM, ITBM and others

About us

With over 20,000 employees across the globe, we work at the heart of digitisation, advising organisations on IT strategy, implementing the most appropriate technology, and helping our customers to source, transform and manage their technology infrastructure in over 70 countries. We deliver digital technology to some of the world’s greatest organisations, driving digital transformation, and enabling people and their businesses. 

Learning and development

Our people are our strength which is why we offer leadership training, coaching, mentoring, professional development, and international opportunities. Whichever direction you choose to go in – whether it’s a well-trodden path or a completely new part of the business.

You belong.

We passionately believe in the power of diversity and inclusion. We celebrate our differences because we know a diverse workforce with different experiences and perspectives helps us win together. And to do that, you need to feel comfortable to bring your whole self to work – and you can only do that when you feel supported, valued, and have a sense of belonging which is what we strive to achieve. Your application is considered on its merits regardless of your age, disability, ethnicity, faith, gender identity or sexual orientation. All that matters to us is that you share our vision and our values, and that you bring the experience and skills we need. We are proud to be a Disability Confident Employer, we welcome applications from people with a disability – and guarantee to interview applicants who have a disability and meet the essential requirements for the job.

Contains bad word ""Technical Lead"". Skipping this job!
",Skipped,Not Available
**********,https://www.linkedin.com/jobs/view/**********,Pending,2025-07-23 17:44:47.075290,2025-07-25 17:44:47.260379,Found a Bad Word in About Job,"
About the job
Onsurity is a rapidly growing employee healthcare benefits platform that provides flexible and customised healthcare subscriptions for SMEs, start-ups, and enterprises. We believe that access to healthcare benefits shouldn’t be a luxury. It is this philosophy that has strengthened our commitment towards making healthcare affordable and accessible for all.
Our subscriptions include discounts on medicine orders, health checkups, fitness plans, free doctor teleconsultations, and insurance, among other benefits. We believe in inclusivity which means our plans are not limited to full-time employees. We also cover contractual workers, interns, freelancers, and consultants.
We encourage you to read more about us on www.onsurity.com. You can also find us on LinkedIn, Instagram, and YouTube.
Below are stories that define our journey and showcase our commitment to democratizing healthcare across the country.
Onsurity is providing healthcare membership to SMEs with as low as three employees
The Journey Of Startups: Journey Onsurity
Cricketer Anil Kumble backs Onsurity as strategic advisor
Onsurity partners with Gulf Oil to offer healthcare to 10,000 truckers
83% Indian Employees Unaware Of Employer-Provided Healthcare Benefits, Says Study
News: Onsurity secures $45M Series B round led by Creaegis — People Matters

We were also featured in the first season of Disney+ Hotstar's remarkable series, The Great Indian Disruptors.
Our strategic partner and investor, cricketing legend Anil Kumble, is actively involved in our mission to make healthcare more accessible. Anil Kumble recently graced us with his presence at Onsurity’s Bengaluru office and engaged with our employees. He is passionate about our mission and has played an instrumental role in our journey so far.
Recently, Dun & Bradstreet India acknowledged our mission and conferred us with the Dun & Bradstreet Start-up 50 Trailblazer 2023 award.

About the Role:

We are seeking a skilled React Native Developer to join our mobile development team. You will be responsible for building cross-platform mobile applications for iOS and Android using the React Native framework. As part of our tech team, you will collaborate with product managers, designers, and backend developers to deliver high-quality, scalable mobile solutions.

Key Responsibilities:

Develop and maintain cross-platform mobile applications using React Native.
Integrate mobile apps with RESTful APIs and third-party services.
Collaborate with designers to implement intuitive UI/UX designs.
Write clean, maintainable, and well-documented code.
Optimize app performance and troubleshoot issues.
Conduct code reviews and maintain code quality.
Stay up-to-date with emerging technologies and mobile development trends.

Requirements:

2+ years of experience with React Native and JavaScript/TypeScript.
Experience deploying apps to the Apple App Store and Google Play Store.
Solid understanding of mobile architecture, performance optimization, and native device features.
Familiarity with Redux, MobX, or other state management tools.
Experience integrating native modules (e.g., Swift, Objective-C, Java, Kotlin) is a plus.
Understanding of REST APIs, JSON, and offline storage mechanisms.
Familiarity with Git and version control tools.
Excellent problem-solving and communication skills.

Contains bad word ""Product Manager"". Skipping this job!
",Skipped,Not Available
4270223783,https://www.linkedin.com/jobs/view/4270223783,Pending,2025-07-21 17:44:53.715586,2025-07-25 17:44:54.272121,Found a Bad Word in About Job,"
About the job
Job Title: Fullstack Developer

Experience Required: 2–4 years

Location: Bengaluru (Hybrid) (3 days WFO, 2 days WFH)

Employment Type: Full-time

Key Responsibilities

Design, develop, test, and maintain scalable fullstack applications using modern technologies.
Implement responsive and cross-browser compatible UI components with HTML, CSS (SASS/LESS), and JavaScript.
Develop robust backend services and APIs using Node.js, Express.js, JavaScript/TypeScript.
Collaborate with cross-functional teams including designers, product managers, and other developers.
Ensure code quality through best practices, including unit testing and code reviews.
Contribute to architectural decisions and provide innovative solutions to complex challenges.

Must-Have Qualifications

2–3 years of professional experience as a Fullstack Engineer.
Strong proficiency in HTML, CSS (LESS/SASS), and JavaScript with a deep understanding of responsive and cross-browser web design principles.
Hands-on experience with modern front-end frameworks such as React or Angular.
Solid backend development experience in Node.js, Express.js, and either JavaScript or TypeScript.
Strong grasp of software engineering fundamentals, including data structures, algorithms, and problem-solving skills.

Good-to-Have Skills

Experience building applications that integrate AI tools or AI-driven workflows.
Exposure to backend development using Python or Java.
Knowledge of databases, including MongoDB, DynamoDB, MySQL, Redis, ElastiCache, and ElasticSearchDB.
Experience designing and developing RESTful APIs, preferably with metric-driven API Gateway integrations.
Familiarity with AWS services, Kubernetes, microservices, and domain-driven architecture.
Excellent written and verbal communication skills, with the ability to clearly present technical concepts to stakeholders and team members.

Skills: angular,typescript,html,node.js,express.js,javascript,react,css,restful apis,css (sass/less)

Contains bad word ""Product Manager"". Skipping this job!
",Skipped,Not Available
4275556779,https://www.linkedin.com/jobs/view/4275556779,Pending,2025-07-25 11:45:22.851221,2025-07-25 17:45:23.140260,Found a Bad Word in About Job,"
About the job
Job Title : C/C++ Developer
Location State : Karnataka
Location City : Bangalore
Experience Required : 2+ Year(s)
Shift: General
Work Mode: Hybrid
Position Type: Contract

Company Name: VARITE INDIA PRIVATE LIMITED

About The Client: An American multinational digital communications technology conglomerate corporation headquartered in San Jose, California. The Client develops, manufactures, and sells networking hardware, software, telecommunications equipment, and other high-technology services and products. The Client specializes in specific tech markets, such as the Internet of Things (IoT), domain security, videoconferencing, and energy management. It is one of the largest technology companies in the world, ranking 82nd on the Fortune 100 with over $51 billion in revenue and nearly 83,300 employees.

Essential Job Functions: 
Work on Linux-based platforms and understand open-source processes. 
Solve complex problems using strong troubleshooting skills. 
Communicate and collaborate effectively, both verbally and in writing. 
Handle ambiguity and prioritize tasks effectively. 
Define problems, analyze facts, and develop logical solutions. 
Foster teamwork and resolve issues positively. 

Qualifications: 
Experience Range: 2 to 3 years 
 Skills Required: 
Programming Languages: C/C++. 
Platform: Linux 
Core Concepts: Multithreading, Singleton, Algorithms, Data Structures, Object-Oriented Design, and Database concepts. 
Asynchronous Transactions: REST API, Event-driven patterns, IPC, and HTTP transactions. 
Security: TLS, mTLS, certificate management, and ciphers. 
Networking Fundamentals: IP, TCP/UDP, DNS, HTTP. 
Troubleshooting: Debugging functional, scale, and threading issues in Linux OS. 
Agile Experience: Familiarity with Agile development environments.

How to Apply: Interested candidates are invited to submit their resume using the apply online button on this job post.

Equal Opportunity Employer: VARITE is an equal opportunity employer. We celebrate diversity and are committed to creating an inclusive environment for all employees. We do not discriminate on the basis of race, color, religion, sex, sexual orientation, gender identity or expression, national origin, age, marital status, veteran status, or disability status.

Unlock Rewards: Refer Candidates and Earn. If you're not available or interested in this opportunity, please pass this along to anyone in your network who might be a good fit and interested in our open positions. VARITE offers a Candidate Referral program, where you'll receive a one-time referral bonus based on the following scale if the referred candidate completes a three-month assignment with VARITE.

Exp Req - Referral Bonus
0 - 2 Yrs. - INR 5,000
2 - 6 Yrs. - INR 7,500
6 + Yrs. - INR 10,000

About VARITE: VARITE is a global staffing and IT consulting company providing technical consulting and team augmentation services to Fortune 500 Companies in USA, UK, CANADA and INDIA. VARITE is currently a primary and direct vendor to the leading corporations in the verticals of Networking, Cloud Infrastructure, Hardware and Software, Digital Marketing and Media Solutions, Clinical Diagnostics, Utilities, Gaming and Entertainment, and Financial Services.

Contains bad word ""Marketing"". Skipping this job!
",Skipped,Not Available
4275551783,https://www.linkedin.com/jobs/view/4275551783,Pending,2025-07-25 11:45:48.711214,2025-07-25 17:45:48.894096,Found a Bad Word in About Job,"
About the job
Life on the team

Join a dynamic supportive team working together to solve strong technical challenges by building high-quality ServiceNow solutions. Established as one of the first ServiceNow partners in Europe and awarded the 2022 EMEA Elite Segment Partner of the Year, we have grown a team with strong expertise across every aspect of the Now Platform. Our customers look to us for advice, best practice, and well-designed implementations. As well as to solve enterprise-wide process challenges by bringing great user experiences. We are expanding our team of Senior Technical Consultants and are seeking individuals who want to grow with us and progress to become Technical Architects. Through real-world experience and participation in the Certified Master Architect and Certified Technical Architect programs, we offer abundant opportunities for our team members to develop their technical skills and gain client-facing experience.

What you’ll do

Delivering high quality ServiceNow implementations based on business process requirements.
Effective delivery of quality solutions using Computacenter's methodologies and ensuring adherence to coding and design standards, and generating technical documentation
Increasing application operating efficiency and adapting to new requirements, as necessary
Keep up to date with current and future market developments, technologies, product, and strategies.
Attend and present at customer meetings to ensure understanding of customer requirements and to assist with knowledge transfer.
Recording, qualification and questioning of customer requirements, even in complex projects and in the case of unclear customer requirements and standards.
Successful handover of technology to internal or customer support function
we’ll support you to Attain and retain ServiceNow Certifications and partner accreditations.

What you’ll need

8+ years of experience in the ServiceNow Application Development
Configure and customize ServiceNow applications and modules using scripting (JavaScript), workflow, and other development techniques. 
Proven experience in configuring and customizing the ServiceNow platform and Expertise on 4-5 applications ranging from FSM & CSM, SPM, ITAM, Now App Engine and Portals.
Ability to provide technical leadership and support to Business Process Consultants. 
Responsible for leading technical implementations, providing technical design lead and mentoring junior members of the team.
Hands on Skills and/or experience in Web Technologies (e.g., Javascript, SOAP/REST web services, XML & JSON, Angular.js, Seismic)
Product line (CIS) accreditation preferred – Certified or pursuing.
Understanding of Software Development Lifecycle experience in Agile projects
Open and friendly personality, with ability to be customer facing. 
Be self-managing and capable of working alone or as part of a team. 

Experience & Education:

BE / BTech/MCA in Computer Science or related disciple 
Strong knowledge of JavaScript, scripting, and web development.
Familiarity with ITSM processes and ITIL framework.

Certifications

ServiceNow Certified Application Developer
ServiceNow Certified Implementation Specialist - CSM, SPM, ITAM, ITBM and others

About us

With over 20,000 employees across the globe, we work at the heart of digitisation, advising organisations on IT strategy, implementing the most appropriate technology, and helping our customers to source, transform and manage their technology infrastructure in over 70 countries. We deliver digital technology to some of the world’s greatest organisations, driving digital transformation, and enabling people and their businesses. 

Learning and development

Our people are our strength which is why we offer leadership training, coaching, mentoring, professional development, and international opportunities. Whichever direction you choose to go in – whether it’s a well-trodden path or a completely new part of the business.

You belong.

We passionately believe in the power of diversity and inclusion. We celebrate our differences because we know a diverse workforce with different experiences and perspectives helps us win together. And to do that, you need to feel comfortable to bring your whole self to work – and you can only do that when you feel supported, valued, and have a sense of belonging which is what we strive to achieve. Your application is considered on its merits regardless of your age, disability, ethnicity, faith, gender identity or sexual orientation. All that matters to us is that you share our vision and our values, and that you bring the experience and skills we need. We are proud to be a Disability Confident Employer, we welcome applications from people with a disability – and guarantee to interview applicants who have a disability and meet the essential requirements for the job.

Contains bad word ""Technical Lead"". Skipping this job!
",Skipped,Not Available
4272017952,https://www.linkedin.com/jobs/view/4272017952,Pending,2025-07-25 16:45:57.299035,2025-07-25 17:45:57.431099,Found a Bad Word in About Job,"
About the job
Type: Contract (C2H)
Duration: 6 Months 
Location: Bangalore (Hybrid 3 Days )
Rate: 18 LPA
Experience: 6 to 9 Years

Looking for an experienced Hyperion support Developer with 5-8 years of relevant experience. Candidates with financial background is preferred. The detailed responsibilities are mentioned below.
HFM, Planning, Essbase, HPCM
Perform coding and configuration to enhance and maintain Oracle EPM tools or Hyperion applications, including Planning, Essbase (BSO and ASO cubes), FDMEE,HPCM.
Monitor, maintain security, management process controls, task flows, business rules, scripts, member lists, journal module, objects (Webforms, Grids, Task lists), consolidation and data clearing procedures, metadata updates etc.
Strong knowledge of Essbase scripting and Calc Manager Rules, Essbase Calc Scripts, Batch scripts, MDX & MAXL. EPM solution, Essbase Cubes.
Work closely with Master data team and finance teams to manage metadata changes, business rule updates, form/dashboard enhancements, and data loads.
Have good general functional knowledge and understanding of budgeting, forecasting and financial analysis and close processes
Sound knowledge of Life Cycle Management, user Management, Security Management etc
Provide level one and level two support in line with the team’s remit: CoA validations and deployments using EPMA / DRM; User support and setups.
Good analytical, problem solving, & communication skills
Have experience working with ServiceNow ticketing tool, approval process and SLA.
Address user queries with webforms, SmartView, data reconciliation in HFM
 Read, understand and update HFM rules per user requirements
 Update HFM application metadata and security as required
 Re-shell application and reconcile data for regression testing before monthly updates.
 Deploy updates to Pre-Prod and reconcile data before monthly updates in HFM and Planning/Essbase apps.
 Deploy changes to Production after preproduction COA and new enhancements testing.
 FDMEE: Monitor data loading schedule jobs and if any failures/kickouts. Address these by liaising with entity owners (in the case of data quality errors) or updating dimension maps

Contains bad word ""Finance"". Skipping this job!
",Skipped,Not Available
4270223783,https://www.linkedin.com/jobs/view/4270223783,Pending,2025-07-21 17:46:07.253448,2025-07-25 17:46:07.349504,Found a Bad Word in About Job,"
About the job
Job Title: Fullstack Developer

Experience Required: 2–4 years

Location: Bengaluru (Hybrid) (3 days WFO, 2 days WFH)

Employment Type: Full-time

Key Responsibilities

Design, develop, test, and maintain scalable fullstack applications using modern technologies.
Implement responsive and cross-browser compatible UI components with HTML, CSS (SASS/LESS), and JavaScript.
Develop robust backend services and APIs using Node.js, Express.js, JavaScript/TypeScript.
Collaborate with cross-functional teams including designers, product managers, and other developers.
Ensure code quality through best practices, including unit testing and code reviews.
Contribute to architectural decisions and provide innovative solutions to complex challenges.

Must-Have Qualifications

2–3 years of professional experience as a Fullstack Engineer.
Strong proficiency in HTML, CSS (LESS/SASS), and JavaScript with a deep understanding of responsive and cross-browser web design principles.
Hands-on experience with modern front-end frameworks such as React or Angular.
Solid backend development experience in Node.js, Express.js, and either JavaScript or TypeScript.
Strong grasp of software engineering fundamentals, including data structures, algorithms, and problem-solving skills.

Good-to-Have Skills

Experience building applications that integrate AI tools or AI-driven workflows.
Exposure to backend development using Python or Java.
Knowledge of databases, including MongoDB, DynamoDB, MySQL, Redis, ElastiCache, and ElasticSearchDB.
Experience designing and developing RESTful APIs, preferably with metric-driven API Gateway integrations.
Familiarity with AWS services, Kubernetes, microservices, and domain-driven architecture.
Excellent written and verbal communication skills, with the ability to clearly present technical concepts to stakeholders and team members.

Skills: angular,typescript,html,node.js,express.js,javascript,react,css,restful apis,css (sass/less)

Contains bad word ""Product Manager"". Skipping this job!
",Skipped,Not Available
4270239677,https://www.linkedin.com/jobs/view/4270239677,Pending,2025-07-21 17:46:25.179757,2025-07-25 17:46:25.500503,Found a Bad Word in About Job,"
About the job
Position - Frontend - Mobile RN Developer

Experience - 5+ Years

Location - Multiple locations across India. Here are some of the key locations:
Bangalore
Bhubaneswar
Chennai
Coimbatore
Gandhinagar
Gurugram
Hyderabad
Kolkata
Mumbai
Noida
Pune
Salem
Tiruchirappalli

Must Have Skills - Native module , Axios , Custom hooks , Husky , SonarQube , Redux , info.plist , manisfest.xml 

Must Have:
Expertise in strategizing and developing mobile applications for both iOS and Android platforms using React Native.
5+ years of hands-on experience in building and designing mobile applications with React Native - TypeScript.
Strong proficiency with Redux-Saga for managing application state and handling side effects.
Solid knowledge of JavaScript, TypeScript, Swift, and Kotlin for cross-platform and native mobile development.
Experience integrating third-party libraries into React Native apps, such as Firebase, Sentry, and others for push notifications, analytics, and crash reporting.
Ability to build and design reusable NPM packages for multiple projects, promoting efficient code sharing.
Proven experience developing custom native modules for at least one platform (iOS with Swift/Obj-C or Androidwith Java/Kotlin).
Proficient in creating React Native components that are efficient, maintainable, and easy to test.
Strong knowledge of unit testing and writing test cases using Jest to ensure high code quality.
Version control using Git to maintain a clean and organized codebase.
Experience working with design systems such as Atomic Design or Fabric to maintain consistency across applications.
Familiarity with Figma to translate design specs into well-crafted, functional mobile interfaces.
Comfortable using collaboration tools like JIRA, Confluence, and other project management software to track progress and communicate effectively within teams.

About CLPS RiDiK
RiDiK is a global technology solutions provider and a subsidiary of CLPS Incorporation (NASDAQ: CLPS), delivering cutting-edge end-to-end services across banking, wealth management, and e-commerce. With deep expertise in AI, cloud, big data, and blockchain, we support clients across Asia, North America, and the Middle East in driving digital transformation and achieving sustainable growth. Operating from regional hubs in 10 countries and backed by a global delivery network, we combine local insight with technical excellence to deliver real, measurable impact. Join RiDiK and be part of an innovative, fast-growing team shaping the future of technology across industries.

Contains bad word ""Blockchain"". Skipping this job!
",Skipped,Not Available
