Job ID,Title,Company,Work Location,Work Style,About Job,Experience required,Skills required,HR Name,HR Link,Resume,Re-posted,Date Posted,Date Applied,Job Link,External Job link,Questions Found,Connect Request
4271865840,Application Specialist,Maven Consulting Service,ven Consulting Service,ven Consulting Service,"About the job
Company Description
 We suggest you enter details here.
 Role Description
 This is a full-time on-site role for an Application Specialist located in Bengaluru. The Application Specialist will be responsible for the implementation and support of software applications, troubleshooting and resolving issues, collaborating with the development team to enhance application functionality, and providing user training and support. Other responsibilities will include system testing, documentation of processes, and ensuring applications meet business requirements and industry standards.
 Qualifications
  Experience in software implementation and support, troubleshooting, and issue resolution
Skills in collaborating with development teams to enhance application functionality
Proficiency in system testing and documentation of processes
Strong user training and support skills
Excellent problem-solving and analytical skills
Effective written and verbal communication skills
Familiarity with industry standards and regulatory requirements
Bachelor’s degree in Computer Science, Information Technology, or related field",0,"{'error': '429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {\n  quota_metric: ""generativelanguage.googleapis.com/generate_content_free_tier_requests""\n  quota_id: ""GenerateRequestsPerDayPerProjectPerModel-FreeTier""\n  quota_dimensions {\n    key: ""model""\n    value: ""gemini-1.5-flash""\n  }\n  quota_dimensions {\n    key: ""location""\n    value: ""global""\n  }\n  quota_value: 50\n}\n, links {\n  description: ""Learn more about Gemini API quotas""\n  url: ""https://ai.google.dev/gemini-api/docs/rate-limits""\n}\n, retry_delay {\n  seconds: 35\n}\n]'}",Rajshekar Tubachi ,https://www.linkedin.com/in/rajshekar-tubachi,Previous resume,False,2025-07-25 08:30:24.468648,2025-07-25 10:30:28.692461,https://www.linkedin.com/jobs/view/4271865840,Easy Applied,"{('Phone country code [ ""List of phone country codes"" ]', 'Yes', 'select', 'India (+91)'), ('phone', '9686355952', 'text', '9686355952'), ('Email [  ""Select an option"", ""<EMAIL>"", ]', 'Yes', 'select', '<EMAIL>')}",In Development
**********,Software Test Engineer (Cypress / Typescript),Avensys Consultin,ensys Consultin,ensys Consultin,"About the job
Avensys is a reputed global IT professional services company headquartered in Singapore. Our service spectrum includes enterprise solution consulting, business intelligence, business process automation and managed services. Given our decade of success we have evolved to become one of the top trusted providers in Singapore and service a client base across banking and financial services, insurance, information technology, healthcare, retail, and supply chain.

We are currently looking to hire Software Test Engineer.

This is an exciting opportunity to expand your skill set, achieve job satisfaction and work-life balance. More details as below.

Job Type: One-year Renewable Contract

Job Description:
We seek individuals who share our passion and determination. Our commitment to customer
success drives us to go the extra mile. We expect you to have experience in frontend
development, and it’s further expected that you have an open mindset, a desire to
collaborate, and that you thrive on constant new challenges.
We also prefer that you have an education in software development or a related field. If
you’re ready to join us on this mission, take a closer look at the minimum criteria for the
position.
• Proven experience in testing web applications
• Strong understanding of software QA methodologies, tools, and processes
• Familiarity with browser developer tools and debugging techniques
• Experience with bug tracking and test management tools
• Excellent analytical and problem-solving skills
• Familiarity with automated testing frameworks e.g Cypress
• Agile/Scrum methodologies
• Working with Europe counterpart engineering teams
• Good communication skills and proficiency in English

WHAT’S ON OFFER
You will be remunerated with an excellent base salary and entitled to attractive company benefits. Additionally, you will get the opportunity to enjoy a fun and collaborative work environment, alongside a strong career progression.
To submit your application, please apply online or email your UPDATED CV in Microsoft Word <NAME_EMAIL>. Your interest will be treated with strict confidentiality.

CONSULTANT DETAILS
Consultant Name: Preethi Kanthappan
Reg No: R1765546
Avensys Consulting Pte Ltd
EA Licence 12C5759
Privacy Statement: Data collected will be used for recruitment purposes only. Personal data provided will be used strictly in accordance with the relevant data protection law and Avensys' privacy policy.",0,"{'error': '429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {\n  quota_metric: ""generativelanguage.googleapis.com/generate_content_free_tier_requests""\n  quota_id: ""GenerateRequestsPerDayPerProjectPerModel-FreeTier""\n  quota_dimensions {\n    key: ""model""\n    value: ""gemini-1.5-flash""\n  }\n  quota_dimensions {\n    key: ""location""\n    value: ""global""\n  }\n  quota_value: 50\n}\n, links {\n  description: ""Learn more about Gemini API quotas""\n  url: ""https://ai.google.dev/gemini-api/docs/rate-limits""\n}\n, retry_delay {\n  seconds: 22\n}\n]'}",Unknown,Unknown,Previous resume,False,2025-07-25 08:30:36.942401,2025-07-25 10:30:41.470939,https://www.linkedin.com/jobs/view/**********,Easy Applied,"{('Phone country code [ ""List of phone country codes"" ]', 'Yes', 'select', 'India (+91)'), ('phone', '9686355952', 'text', '9686355952'), ('Email [  ""Select an option"", ""<EMAIL>"", ]', 'Yes', 'select', '<EMAIL>')}",In Development
4275340264,PriceFX Configuration Engineer,NeerInfo Solution,erInfo Solution,erInfo Solution,"About the job
Location -PAN India
Years of Experience- 2 to 20 years.

Qualifications Basic
Bachelor’s degree or foreign equivalent required from an accredited institution. Will also consider three years of progressive experience in the specialty in lieu of every year of education
At least 2 years of experience in Information Technology
Must have: Software package Implementation of PriceFx 
PriceFx Certification is a plus.

Technical Skills:
Strong understanding of enterprise architecture frameworks
In-depth knowledge of Pricefx Platform including Price Builder, Deal Manager, Rebate Manager, Quote configurator, Channel Manager
Strong understanding of Pricefx Configuration Studio (CS) and Pricefx promotion engine 
Strong proficiency in JavaScript for UI extensions and dynamic page behavior
Expertise in Groovy scripting within Pricefx for logic implementation and customization
Expertise in Integrating Pricefx with ERP using ETL tools, RESTful API’s, SOAP and custom connectors
Strong knowledge of Security best practices.",2,"{'error': '429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {\n  quota_metric: ""generativelanguage.googleapis.com/generate_content_free_tier_requests""\n  quota_id: ""GenerateRequestsPerDayPerProjectPerModel-FreeTier""\n  quota_dimensions {\n    key: ""model""\n    value: ""gemini-1.5-flash""\n  }\n  quota_dimensions {\n    key: ""location""\n    value: ""global""\n  }\n  quota_value: 50\n}\n, links {\n  description: ""Learn more about Gemini API quotas""\n  url: ""https://ai.google.dev/gemini-api/docs/rate-limits""\n}\n, retry_delay {\n  seconds: 39\n}\n]'}",Mayuri Jain ,https://www.linkedin.com/in/mayuri-jain-7290481b5,Previous resume,False,2025-07-24 16:31:19.975464,2025-07-25 10:31:24.718913,https://www.linkedin.com/jobs/view/4275340264,Easy Applied,"{('how much experience do you have in java/groovy?', '3', 'text', ''), ('mobile phone number', '9686355952', 'text', '9686355952'), ('Do you have experience in PriceFx or have any of its certification? [  ""Select an option"", ""Yes"", ""No"", ]', 'Yes', 'select', 'Select an option'), ('Phone country code [ ""List of phone country codes"" ]', 'Yes', 'select', 'India (+91)'), ('Email address [  ""Select an option"", ""<EMAIL>"", ]', 'Yes', 'select', '<EMAIL>')}",In Development
4275333071,Data Engineer,Tredence Inc,edence Inc,edence Inc,"About the job
Job Title: Data Engineer
Experience: 12 to 20 months
Work Mode: Work from Office
Locations: Bangalore, Chennai, Kolkata, Pune, Gurgaon

About Tredence
Tredence focuses on last-mile delivery of powerful insights into profitable actions by uniting its strengths in business analytics, data science, and software engineering. The largest companies across industries are engaging with us and deploying their prediction and optimization solutions at scale. Headquartered in the San Francisco Bay Area, we serve clients in the US, Canada, Europe, and Southeast Asia.
Tredence is an equal opportunity employer. We celebrate and support diversity and are committed to creating an inclusive environment for all employees.
🌐 Visit: www.tredence.com

Role Overview
We are seeking a driven and hands-on Data Engineer with 12 to 20 months of experience to support modern data pipeline development and transformation initiatives on Google Cloud Platform (GCP). The role requires strong technical skills in SQL, Python, and PySpark, along with experience in GCP-native tools and data engineering fundamentals.

Key Responsibilities
Develop scalable data pipelines using PySpark and GCP services like Dataflow and BigQuery
Write efficient SQL for data transformation, validation, and analysis
Implement and support data warehouse principles including:
Fact & Dimension modeling
Star/Snowflake schemas
Slowly Changing Dimensions (SCD)
Change Data Capture (CDC)
Medallion Architecture
Ensure high performance and data quality through monitoring and troubleshooting
Collaborate with cross-functional teams and clients to deliver actionable data solutions
Participate in agile sprints and contribute technical insights

Mandatory Skills
Strong hands-on experience with SQL and Python
Proficient in PySpark for large-scale data processing
Exposure to Google Cloud Platform (GCP)
Solid understanding of data warehousing fundamentals
Excellent problem-solving and debugging skills
Strong verbal and written communication

Preferred Skills
Experience with BigQuery, Cloud Dataflow, or Pub/Sub
Knowledge of CI/CD processes and version control (e.g., Git)
Exposure to Agile/Scrum practices
Understanding of structured/semi-structured formats (e.g., JSON, Parquet)",0,"{'error': '429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {\n  quota_metric: ""generativelanguage.googleapis.com/generate_content_free_tier_requests""\n  quota_id: ""GenerateRequestsPerDayPerProjectPerModel-FreeTier""\n  quota_dimensions {\n    key: ""model""\n    value: ""gemini-1.5-flash""\n  }\n  quota_dimensions {\n    key: ""location""\n    value: ""global""\n  }\n  quota_value: 50\n}\n, links {\n  description: ""Learn more about Gemini API quotas""\n  url: ""https://ai.google.dev/gemini-api/docs/rate-limits""\n}\n, retry_delay {\n}\n]'}",Unknown,Unknown,Previous resume,False,2025-07-24 14:31:58.714707,2025-07-25 10:32:04.237167,https://www.linkedin.com/jobs/view/4275333071,Easy Applied,"{('mobile phone number', '9686355952', 'text', '9686355952'), ('Phone country code [ ""List of phone country codes"" ]', 'Yes', 'select', 'India (+91)'), ('how many years of work experience do you have with pyspark?', '1', 'text', '1'), ('what is your notice period duration?', '0', 'text', ''), ('how many years of work experience do you have with sql?', '1', 'text', '1'), ('Email address [  ""Select an option"", ""<EMAIL>"", ]', 'Yes', 'select', '<EMAIL>'), ('how many years of work experience do you have with google bigquery?', '3', 'text', '')}",In Development
4271581325,Azure Integration Engineer,HariNex Solution,riNex Solution,riNex Solution,"About the job
Only Immediate joiner or 15 days Notice

Azure Integration position JD:
Key Responsibilities:
1. Design, develop, and implement Azure integration solutions for various applications/services.
2. Collaborate with cross-functional teams to understand integration requirements and design
scalable solutions.
3. Configure and manage Azure services such as Azure Functions, Logic Apps, Service Bus,
Event Grid, and API Management.
4. Utilize GitHub for version control, branching, merging, and code reviews.
5. Develop CI/CD pipelines using Azure DevOps or GitHub Actions for automated deployment
and testing.
6. Monitor and troubleshoot integration solutions to ensure high availability and performance.
7. Document technical designs, configurations, and processes for knowledge sharing and future
reference.
8. Stay updated with the latest Azure services and features, GitHub best practices, and
integration patterns.
Required Skills:
1. Strong experience with Azure services including but not limited to Azure Functions, Logic
Apps, Service Bus, Event Grid, and API Management.
2. Proficiency in programming languages such as C#, JavaScript.
3. Hands-on experience with GitHub for version control and collaboration.
4. Knowledge of CI/CD concepts and experience with tools like Azure DevOps or GitHub
Actions.
5. Understanding of RESTful APIs, JSON and XML.
6. Ability to work independently and as part of a team in a fast-paced environment.
7. Excellent problem-solving and communication skills.
8. Familiarity with Agile development methodologies.
Preferred Qualifications:
1. Microsoft Azure certifications such as Azure Developer Associate or Azure Solutions
Architect.
2. Experience with other cloud platforms like AWS or Google Cloud Platform.
3. Understanding of enterprise integration patterns and technologies.
4. Previous experience in designing and implementing scalable, distributed systems.",0,"{'error': '429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {\n  quota_metric: ""generativelanguage.googleapis.com/generate_content_free_tier_requests""\n  quota_id: ""GenerateRequestsPerDayPerProjectPerModel-FreeTier""\n  quota_dimensions {\n    key: ""model""\n    value: ""gemini-1.5-flash""\n  }\n  quota_dimensions {\n    key: ""location""\n    value: ""global""\n  }\n  quota_value: 50\n}\n, links {\n  description: ""Learn more about Gemini API quotas""\n  url: ""https://ai.google.dev/gemini-api/docs/rate-limits""\n}\n, retry_delay {\n  seconds: 46\n}\n]'}",Ekta Pandey ,https://www.linkedin.com/in/ekta-pandey-6b11a8190,Previous resume,False,2025-07-24 13:32:13.436001,2025-07-25 10:32:17.192934,https://www.linkedin.com/jobs/view/4271581325,Easy Applied,"{('Phone country code [ ""List of phone country codes"" ]', 'Yes', 'select', 'India (+91)'), ('Email address [  ""Select an option"", ""<EMAIL>"", ]', 'Yes', 'select', '<EMAIL>'), ('mobile phone number', '9686355952', 'text', '9686355952')}",In Development
4271581304,System Engineer,NewSpace Research and Technologie,wSpace Research and Technologie,wSpace Research and Technologie,"About the job
Who we are:

We are an innovative startup with offices in Bengaluru and Delhi NCR, dedicated to revolutionizing India's defence capabilities through the development of next-generation technologies. Our mission focuses on advancing unmanned vehicle technology, pioneering robotic swarm systems, and pushing the boundaries of aerospace innovation to address the evolving needs of the Indian defence forces. Our current projects showcase our commitment to cutting-edge solutions, including a solar-powered High Altitude Pseudo Satellite (HAPS) - an unmanned stratospheric platform designed for extended endurance missions, and a versatile Stand-off Autonomous System that can be launched from air or ground for advanced military applications. We are seeking passionate innovators ready to shape the future of warfare technology and contribute significantly to India's defence preparedness. Join us in our quest to develop ground breaking solutions that will define the next era of military capabilities.

We are seeking a highly skilled Systems Engineer with a minimum of 5 years of experience in systems engineering, particularly in the aerospace or mechanical fields. The ideal candidate will have a strong background in system requirement decomposition and be adept at various systems engineering tasks. This role requires a proactive individual who can work collaboratively within a team to design, implement, and maintain complex systems.
 Roles and Responsibilities:

Define, analyse and maintain System Requirements. Support decomposition to sub-system and lower levels. Maintain compliance status. 
Define and maintain System & Sub-system V&V plans with acceptance criteria.
Maintain System specifications, MVP budgets, configuration, engineering changes and interfaces.
Aid in defining Design philosophy to meet project schedule and cost. 
Collaborate with the Project management (PM) team to prepare Project Work breakdown Structure (WBS) and manage Project Resource, Budget and Schedule.
Coordinate and track design activities. Identify critical areas for system development. 
Review engineering change requests and assess their impact on system development. 
Conduct CFT discussions and design reviews. Enable & take design decisions at appropriate level. Escalate issues that impact technical risk, cost and schedule to the stakeholders. 
Coordinate data exchange and management between subsystems through Interface Control Documents (ICDs)
Perform Technical risk assessment and identify mitigation strategies. Track and manage design risks throughout the development lifecycle. 
Establish and Implement SE processes as part of the product development lifecycle.
Prepare System Technical documentation. Maintain Project Technical documentation.

Must have skills:

5+ years of experience as a systems engineer working in the Aerospace domain.
Demonstrated experience in Systems Engineering disciplines.
Proficient in systems engineering methodologies and tools.
Strong understanding of system architecture and design principles.
Familiarity with UAV/Aircraft design process. 
Strong verbal and written communication skills. 
Self-driven and accountable for the delivery of work. 
Ability to work independently and productively with minimal supervision.

Good to have skills:

Experience with the development lifecycle of a UAV from concept to flight testing. 
Proficient with the various subsystems that comprise a fixed wing UAV, their functionality and interdependencies.
Knowledge of regulatory standards and compliance in the aerospace industry.
Familiar with INCOSE standards for Systems Engineering.
 Basic Requirement (education & relevant experience):

Bachelor’s Degree (Aerospace/Aeronautics, Mechanical) in Engineering. Advanced degree preferred.

This position offers an exciting opportunity to contribute to innovative projects within a dynamic engineering environment. If you are a motivated systems engineer looking to make an impact, we encourage you to apply.",5,"{'error': '429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {\n  quota_metric: ""generativelanguage.googleapis.com/generate_content_free_tier_requests""\n  quota_id: ""GenerateRequestsPerDayPerProjectPerModel-FreeTier""\n  quota_dimensions {\n    key: ""model""\n    value: ""gemini-1.5-flash""\n  }\n  quota_dimensions {\n    key: ""location""\n    value: ""global""\n  }\n  quota_value: 50\n}\n, links {\n  description: ""Learn more about Gemini API quotas""\n  url: ""https://ai.google.dev/gemini-api/docs/rate-limits""\n}\n, retry_delay {\n  seconds: 35\n}\n]'}",Anmol S. ,https://www.linkedin.com/in/anmol-s-889323108,Previous resume,False,2025-07-24 13:32:23.898033,2025-07-25 10:32:31.644690,https://www.linkedin.com/jobs/view/4271581304,Easy Applied,"{('mobile phone number', '9686355952', 'text', '9686355952'), ('Email address [  ""Select an option"", ""<EMAIL>"", ]', 'Yes', 'select', '<EMAIL>'), ('what is your notice period?', '0', 'text', ''), ('Phone country code [ ""List of phone country codes"" ]', 'Yes', 'select', 'India (+91)'), ('what is your expected ctc?', '1000000', 'text', ''), ('what is your current ctc?', '800000', 'text', '')}",In Development
4275260602,Data Engineer (Power BI),Acronotics Limite,ronotics Limite,ronotics Limite,"About the job
Company Description
Acronotics Limited specializes in cutting-edge robotic automation and artificial intelligence solutions. By applying human intelligence to build advanced AI-fueled systems, Acronotics transforms businesses with technologies like AI and Robotic Process Automation (RPA). As a consulting and services firm, we are dedicated to creating automated solutions that will redefine how products are made, sold, and consumed. Our mission is to help clients implement and run game-changing robotic automation and artificial intelligence-based solutions. Explore our product, Radium AI, which automates bot monitoring and support activities, on our website: Radium AI.

Role Description
This is a full-time, on-site role for a Data Engineer (Power BI) based in Bengaluru. You will design and manage data pipelines that connect Power BI, OLAP cubes, documents (pdfs, presentations) and external data sources to Azure AI. Your role ensures structured and unstructured financial data is indexed and accessible for semantic search and LLM use.

Key Responsibilities:
Extract data from Power BI datasets, semantic models, and OLAP cubes.
Connect and transform data via Azure Synapse, Data Factory, and Lakehouse architecture.
Preprocess PDFs, PPTs, and Excel files using Azure Form Recognizer or Python-based tools.
Design data ingestion pipelines for external web sources (e.g., commodity prices).
Coordinate with AI engineers to feed cleaned and contextual data into vector indexes.

Requirements:
Strong experience with Power BI REST/XMLA APIs.
Expertise in OLAP systems (SSAS, SAP BW), data modelling, and ETL design.
Hands-on experience with Azure Data Factory, Synapse, or Data Lake.
Familiarity with JSON, DAX, M queries.",0,"{'error': '429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {\n  quota_metric: ""generativelanguage.googleapis.com/generate_content_free_tier_requests""\n  quota_id: ""GenerateRequestsPerDayPerProjectPerModel-FreeTier""\n  quota_dimensions {\n    key: ""model""\n    value: ""gemini-1.5-flash""\n  }\n  quota_dimensions {\n    key: ""location""\n    value: ""global""\n  }\n  quota_value: 50\n}\n, links {\n  description: ""Learn more about Gemini API quotas""\n  url: ""https://ai.google.dev/gemini-api/docs/rate-limits""\n}\n, retry_delay {\n  seconds: 36\n}\n]'}",Unknown,Unknown,Previous resume,False,2025-07-24 10:33:23.467267,2025-07-25 10:33:28.697356,https://www.linkedin.com/jobs/view/4275260602,Easy Applied,"{('mobile phone number', '9686355952', 'text', '9686355952'), ('We must fill this position urgently. Can you start immediately? [  ""Yes""<Yes>, ""No""<No>, ]', '""Yes""<Yes>', 'radio', '""Yes""<Yes>'), ('Have you built data pipelines that connect Power BI and OLAP cubes with Azure AI? [  ""Select an option"", ""Yes"", ""No"", ]', 'Yes', 'select', 'Select an option'), ('Are you based in Bengaluru? [  ""Select an option"", ""Yes"", ""No"", ]', 'Yes', 'select', 'Select an option'), ('Phone country code [ ""List of phone country codes"" ]', 'Yes', 'select', 'India (+91)'), ('Email address [  ""Select an option"", ""<EMAIL>"", ]', 'Yes', 'select', '<EMAIL>'), ('how many years of experience do you have as a data engineer?', '3', 'text', '')}",In Development
**********,Generative AI Engineer,Indegen,degen,degen,"About the job
We are a technology-led healthcare solutions provider. We are driven by our purpose to enable healthcare organizations to be future-ready. We offer accelerated, global growth opportunities for talent that’s bold, industrious, and nimble. With Indegene, you gain a unique career experience that celebrates entrepreneurship and is guided by passion, innovation, collaboration, and empathy. To explore exciting opportunities at the convergence of healthcare and technology, check out www.careers.indegene.com

Looking to jump-start your career? 

We understand how important the first few years of your career are, which create the foundation of your entire professional journey. At Indegene, we promise you a differentiated career experience. You will not only work at the exciting intersection of healthcare and technology but also will be mentored by some of the most brilliant minds in the industry. We are offering a global fast-track career where you can grow along with Indegene’s high-speed growth.

We are purpose-driven. We enable healthcare organizations to be future ready and our customer obsession is our driving force. We ensure that our customers achieve what they truly want. We are bold in our actions, nimble in our decision-making, and industrious in the way we work.

If this excites you, then apply below.

Role : Sr. Backend Engineer – GenAI Services

Description: We are looking for passionate Backend Engineers to build scalable and secure APIs that power GenAI systems. You will collaborate with architecture, DevOps, and AI teams to support RAG and LLM-based workflows.

Responsibilities

• Develop APIs for serving LLM results and handling embedding-based search (RAG)
• Implement queueing, async jobs, caching layers, and modular services
• Write and test secure Python backend code using FastAPI
• Integrate vector DBs and retrieval systems into backend pipelines
• Participate in PR reviews and contribute to platform reliability

Experience: 3–5 years

Tech Stack

• - Python, FastAPI, PostgreSQL, Git
• - Redis, Docker, RabbitMQ
• - RAG Systems: Connect to vector DBs for RAG, implement metadata-based Schema RAG integrations

Cloud & Deployment

• AWS (RDS, ECS, Lambda), GCP, or Azure
• AI Tools & Productivity Stack
• GitHub Copilot, PR reviewers
• Security & Compliance
• Token and session handling, access management, logging, rate limiting

Indegene is proud to be an Equal Employment Employer and is committed to the culture of Inclusion and Diversity. We do not discriminate on the basis of race, religion, sex, colour, age, national origin, pregnancy, sexual orientation, physical ability, or any other characteristics. All employment decisions, from hiring to separation, will be based on business requirements, the candidate’s merit and qualification. We are an Equal Opportunity Employer. All qualified applicants will receive consideration for employment without regard to race, colour, religion, sex, national origin, gender identity, sexual orientation, disability status, protected veteran status, or any other characteristics.",5,"{'error': '429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {\n  quota_metric: ""generativelanguage.googleapis.com/generate_content_free_tier_requests""\n  quota_id: ""GenerateRequestsPerDayPerProjectPerModel-FreeTier""\n  quota_dimensions {\n    key: ""model""\n    value: ""gemini-1.5-flash""\n  }\n  quota_dimensions {\n    key: ""location""\n    value: ""global""\n  }\n  quota_value: 50\n}\n, links {\n  description: ""Learn more about Gemini API quotas""\n  url: ""https://ai.google.dev/gemini-api/docs/rate-limits""\n}\n, retry_delay {\n  seconds: 49\n}\n]'}",Saptarshi Munshi ,https://www.linkedin.com/in/saptarshi-munshi-b6ba2144,schin_m resume.pdf,False,2025-07-24 10:39:10.653009,2025-07-25 10:39:16.118572,https://www.linkedin.com/jobs/view/**********,Easy Applied,"{('Phone country code [ ""List of phone country codes"" ]', 'Yes', 'select', 'India (+91)'), ('mobile phone number', '9686355952', 'text', '9686355952'), ('We must fill this position urgently. Can you start immediately? [  ""Yes""<Yes>, ""No""<No>, ]', '""Yes""<Yes>', 'radio', '""Yes""<Yes>'), ('Email address [  ""Select an option"", ""<EMAIL>"", ]', 'Yes', 'select', '<EMAIL>')}",In Development
4261915602,SDE-II (Frontend - React JS Development) (3yrs+) (Immediate Joiners Preferred)),TMRW House of Brand,RW House of Brand,RW House of Brand,"About the job
Responsibilities
Write extensive, efficient code to address complex modules and that handles interaction between multiple components.
Rapidly iterate to add new functionalities and solves complex problems with simple and intuitive solutions
Produce architecture with clean interfaces, that are efficient and scalable
Participate and contribute to architectural discussions
Perform design and code reviews
Perform technical feasibility and trade off studies & guides others to perform these studies
Solve production issues. Investigate and provide solutions to minimise the business impact due to outage
Continuously improve performance metrics of modules you own.
Participate in the hiring process (referrals, interviews, attending recruiting events or writing blogs)
Onboard and mentors new team members, helps shape the culture of the team
Collaborate effectively across teams to solve problems, execute and deliver results

Requirements
BS/BE/Tech in Computer science or related streams
4+ years of professional experience in software development
Strong proficiency in Flask or FastAPI in backend / React.js on frontend
Experience in product based companies or startups.
Experience with database design, modelling and implementation (e.g. PostgreSQL, MySQL, MongoDB)
Proficient in writing efficient and scalable code
Familiarity with Git, AWS, and Agile methodologies
Excellent problem-solving and communication skills
Ability to work in a fast-paced environment and prioritize multiple tasks.
Exposure to Spark and/or Big data ecosystem is a huge plus",4,"{'error': '429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {\n  quota_metric: ""generativelanguage.googleapis.com/generate_content_free_tier_requests""\n  quota_id: ""GenerateRequestsPerDayPerProjectPerModel-FreeTier""\n  quota_dimensions {\n    key: ""model""\n    value: ""gemini-1.5-flash""\n  }\n  quota_dimensions {\n    key: ""location""\n    value: ""global""\n  }\n  quota_value: 50\n}\n, links {\n  description: ""Learn more about Gemini API quotas""\n  url: ""https://ai.google.dev/gemini-api/docs/rate-limits""\n}\n, retry_delay {\n  seconds: 21\n}\n]'}",Unknown,Unknown,schin_m resume.pdf,True,2025-07-22 10:39:38.473253,2025-07-25 10:39:44.948447,https://www.linkedin.com/jobs/view/4261915602,Easy Applied,"{('what would be your salary expectations?', '1000000', 'text', ''), ('Are you comfortable in relocating to Bangalore [Umiya Business Bay Tower-1, Kaverappa Layout, Kadubeesanahalli, Kadabeesanahalli, Bengaluru, Karnataka] [  ""Select an option"", ""Yes"", ""No"", ]', 'Yes', 'select', 'Select an option'), ('Email address [  ""Select an option"", ""<EMAIL>"", ]', 'Yes', 'select', '<EMAIL>'), ('what is the current fixed ctc? (in lakhs)', '800000', 'text', ''), ('how many years of relevant experience do you have in frontend?', '3', 'text', ''), ('mobile phone number', '9686355952', 'text', '9686355952'), ('how many years of work experience do you have with react.js?', '1', 'text', '1'), (""what's the duration of your notice period? (in months)"", '0', 'text', ''), ('Phone country code [ ""List of phone country codes"" ]', 'Yes', 'select', 'India (+91)')}",In Development
4270985446,Lead Infrastructure Developer -10+ years - Bengaluru,Tekgence In,kgence In,kgence In,"About the job
Lead Infrastructure Developer 

Location: Bangalore, India (Work from Office)
contract duration : 12+ months

Requirement is for experienced professionals who can architect automation solutions using Ansible, Python, Django, Jenkins/Jules, and DevOps. Additionally, they should have expertise in Datacenter Fabric involving Cisco and Arista Fabrics.

So we are looking for someone who has expertise in Datacenter Fabric along with strong expertise in Automation technologies.",0,"{'error': '429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {\n  quota_metric: ""generativelanguage.googleapis.com/generate_content_free_tier_requests""\n  quota_id: ""GenerateRequestsPerDayPerProjectPerModel-FreeTier""\n  quota_dimensions {\n    key: ""model""\n    value: ""gemini-1.5-flash""\n  }\n  quota_dimensions {\n    key: ""location""\n    value: ""global""\n  }\n  quota_value: 50\n}\n, links {\n  description: ""Learn more about Gemini API quotas""\n  url: ""https://ai.google.dev/gemini-api/docs/rate-limits""\n}\n, retry_delay {\n  seconds: 6\n}\n]'}",Somu sekhar,https://www.linkedin.com/in/somu-sekhar-ba929b13a,Previous resume,False,2025-07-24 10:39:53.564896,2025-07-25 10:39:58.259831,https://www.linkedin.com/jobs/view/4270985446,Easy Applied,"{('how many years of work experience do you have with ansible?', '3', 'text', ''), ('how many years of work experience do you have with datacenter fabric ?', '3', 'text', ''), ('Phone country code [ ""List of phone country codes"" ]', 'Yes', 'select', 'India (+91)'), ('Email address [  ""Select an option"", ""<EMAIL>"", ]', 'Yes', 'select', '<EMAIL>'), ('how many years of work experience do you have with python (programming language)?', '2', 'text', '2'), ('mobile phone number', '9686355952', 'text', '9686355952')}",In Development
4270920167,Backend Developer (In-Person – Bengaluru),XUMAN.A,MAN.A,MAN.A,"About the job
Backend Developer (In-Person – Bengaluru)
Location: Bengaluru (In-person only)
Compensation: ₹45,000 – ₹60,000/month
Type: Full-time
Start: Immediate

Future: Fast-track to core team + equity aligned to product growth milestones

About the Role
We’re building something that will reshape how AI engages with the human experience, emotion, character, decision-making, and context.
We’re not just training models. We’re building a living system that understands and adapts to people, and we’re doing it with speed, clarity, and precision.
As our backend developer, you’ll play a pivotal role in shaping the core architecture that powers this system. You’ll work directly with the founder, designer, and AI engineering team — and help structure something real, clean, and scalable.
What You'll Be Doing
Designing and maintaining high-performance backend APIs
Powering systems for behavioral scoring, feedback loops, and user logic
Working with Node.js, Express, MongoDB/PostgreSQL, and relevant APIs
Collaborating with AI engineers to support real-time quantification logic
Shipping clean, modular code with fast iteration
What You Bring
2–4 years of backend development experience
Clear understanding of API design, data structures, and authentication
Prior experience building and deploying systems (live or internal)
Confidence with DB logic, server architecture, and scaling foundations
Must be located in Bengaluru and available in person.

Why This Role Matters
This isn’t a backend role for a feature factory — this is infrastructure for a new category of intelligence. Your work won’t just connect systems — it’ll define the integrity and performance of how human-AI interaction unfolds.

What You’ll Get
Daily access to the founder + core product decisions
Ownership over critical backend logic
Clear path to equity and leadership after key execution milestones
A platform to build something that will go global — from the inside",4,"{'error': '429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {\n  quota_metric: ""generativelanguage.googleapis.com/generate_content_free_tier_requests""\n  quota_id: ""GenerateRequestsPerDayPerProjectPerModel-FreeTier""\n  quota_dimensions {\n    key: ""model""\n    value: ""gemini-1.5-flash""\n  }\n  quota_dimensions {\n    key: ""location""\n    value: ""global""\n  }\n  quota_value: 50\n}\n, links {\n  description: ""Learn more about Gemini API quotas""\n  url: ""https://ai.google.dev/gemini-api/docs/rate-limits""\n}\n, retry_delay {\n  seconds: 42\n}\n]'}",Unknown,Unknown,Previous resume,False,2025-07-23 10:40:17.516434,2025-07-25 10:40:24.522879,https://www.linkedin.com/jobs/view/4270920167,Easy Applied,"{('Are you comfortable commuting to this job\'s location? [  ""Yes""<Yes>, ""No""<No>, ]', 'Yes', 'radio', None), ('What is your level of proficiency in English? [  ""Select an option"", ""None"", ""Conversational"", ""Professional"", ""Native or bilingual"", ]', 'Yes', 'select', 'Professional'), ('Phone country code [ ""List of phone country codes"" ]', 'Yes', 'select', 'India (+91)'), ('Have you deployed or contributed to any production-grade APIs? [  ""Select an option"", ""Yes"", ""No"", ]', 'Yes', 'select', 'Select an option'), ('We must fill this position urgently. Can you start immediately? [  ""Yes""<Yes>, ""No""<No>, ]', '""Yes""<Yes>', 'radio', '""Yes""<Yes>'), ('Email address [  ""Select an option"", ""<EMAIL>"", ]', 'Yes', 'select', '<EMAIL>'), ('mobile phone number', '9686355952', 'text', '9686355952'), ('Are you comfortable working in an onsite setting? [  ""Yes""<Yes>, ""No""<No>, ]', '""Yes""<Yes>', 'radio', '""Yes""<Yes>'), ('Are you based in Bengaluru and available to work in person full-time? [  ""Select an option"", ""Yes"", ""No"", ]', 'Yes', 'select', 'Select an option'), ('how many years of backend experience do you have?', '3', 'text', '')}",In Development
4275538841,Supply Chain Associate,Cafe Coffee Da,fe Coffee Da,fe Coffee Da,"About the job
• Preparing monthly Business Forecast report at Saleable Article Level.
• Preparing the report for Monthly Procurement Planning at BOM Article Level and Raw Materials.
• Discussion with Category Manager on Monthly Procurement Plan of House Keeping Material.
• Coordinating with Category Teams regarding stock receivables from the Suppliers for distribution planning.
• Sharing Monthly Primary Distribution Plan to Warehousing and Logistics Team.
• Preparing Shelf Life report of Warehouse Inventory on weekly basis to flag information regarding stocks with Critical Shelf Life
• First Level Audit of POs and release of the same in ERP for procurement as per Monthly Procurement Plan - L1 Release.
• Monitoring and Closing Stocks in Transit between Warehouses, as well as Warehouse to Café by coordinating with Logistics Team
• Coordinate Requirements with respect to changes and edits in ERP with IT Team.

Educational Qualification :-
 Bachelor’s Degree: Preferably in Supply Chain Management, Business Administration, Commerce, Logistics, or a related field.

Experience :-
 2-4 years of experience in supply chain planning, procurement, inventory management, or a similar role.
Industry Experience: Experience in the retail, food and beverage, or hospitality industry can be beneficial.
 Technical Skills Required :-
 · Proficiency with Enterprise Resource Planning (ERP) systems for managing procurement and inventory processes.
· Advanced proficiency in Microsoft Excel for data analysis and reporting.
· Experience with Power BI for preparing and reviewing various supply chain reports.
· Familiarity with inventory management and auto indent systems",2,"{'error': '429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {\n  quota_metric: ""generativelanguage.googleapis.com/generate_content_free_tier_requests""\n  quota_id: ""GenerateRequestsPerDayPerProjectPerModel-FreeTier""\n  quota_dimensions {\n    key: ""model""\n    value: ""gemini-1.5-flash""\n  }\n  quota_dimensions {\n    key: ""location""\n    value: ""global""\n  }\n  quota_value: 50\n}\n, links {\n  description: ""Learn more about Gemini API quotas""\n  url: ""https://ai.google.dev/gemini-api/docs/rate-limits""\n}\n, retry_delay {\n}\n]'}",Tripti Singh ,https://www.linkedin.com/in/triptisingh040,Previous resume,False,2025-07-25 10:23:58.869816,2025-07-25 10:41:04.722274,https://www.linkedin.com/jobs/view/4275538841,Easy Applied,"{('Email address [  ""Select an option"", ""<EMAIL>"", ]', 'Yes', 'select', '<EMAIL>'), ('total years of experience in supply chain planning, procurement, inventory management ?', '3', 'text', ''), ('how many years of supply chain experience do you currently have?', '3', 'text', ''), ('mobile phone number', '9686355952', 'text', '9686355952'), ('what is your current ctc ?', '800000', 'text', ''), ('what is your expected ctc ?', '1000000', 'text', ''), ('Have you completed the following level of education: Bachelor\'s Degree? [  ""Yes""<Yes>, ""No""<No>, ]', '""Yes""<Yes>', 'radio', '""Yes""<Yes>'), ('Phone country code [ ""List of phone country codes"" ]', 'Yes', 'select', 'India (+91)')}",In Development
**********,Data Engineer - Satellite Mission Operations & Payload,Astrom,trom,trom,"About the job
Join our satellite team as a Data Engineer responsible for building and maintaining the entire data flow, transformation, storage, processing, and archival infrastructure for satellite telemetry (TM) and payload (PL) data. You'll play a critical role in developing robust backend engines to support payload analytics, mission operations and antenna tracking algorithms.

Key Responsibilities

 Satellite Data Analysis:
Orbit Estimation & Pass Prediction 
Payload Data Processing
 Data Warehouse & Timeseries Management
Architect and manage a unified satellite data warehouse for:
TM: Telemetry
TC: Telecommand logs
PL Raw: Unprocessed payload data
PL Processed: Analytics-ready, localized payload results
Implement and optimize timeseries storage (e.g., TimescaleDB or equivalent open-source DB), handling high-volume ingest, retention policies, and storage cost controls
Setup long-term backup and restore logic for all critical datasets with tested disaster-recovery procedures
 Data Integration Pipelines
Collaborate with the engineering team to design and develop custom plugins for encoding, decoding and storing satellite data
 Data Governance, Security & Compliance
Implement and monitor robust data validation, quality checks, and schema evolution for all ingested datasets
Establish strict data retention schedules, historical playback mechanisms, and backup strategies
Ensure data security: manage encryption in transit and at rest, control access privileges, and maintain audit trails
 Analytics Enablement & Reporting
Expose clean, queryable interfaces for satellite operations teams, mission planners, and analytics applications
Enable fast asset search, forecast generation, and bulk historical downloads (e.g., CSV)
Support the payload UI frontend with reliable and performant data APIs
 Automation & Monitoring
Develop high-availability ETL and data transformation services for raw and processed data
Set up proactive monitoring, alerting, and logging for data pipeline health and storage usage
Continuously improve system performance, scalability, and resilience to failures
 Documentation & Collaboration
Maintain comprehensive technical documentation of data models, pipelines, and operational guidelines
Work cross-functionally with software engineers, satellite operations, and payload teams

Requirements

MCA/B.E./B.Tech/M.Tech in Computer Science, Aerospace/Electronics Engineering, Data Science, or relevant field
Programming: Python and/or Java; advanced SQL; experience with at least one time-series or analytical DBMS (e.g., TimescaleDB, InfluxDB, PostgreSQL, MongoDB)
ETL/Data Pipelines: Experience with building and managing real-time and batch ETL/ELT workflows for large data volumes
Orbit Determination (preferred): Proven experience with satellite dynamics, SGP4/TLE models, or astrodynamics libraries (e.g., Orekit, GMAT) is a strong plus
Geospatial/Localization (preferred): Familiar with georeferencing payload data, GIS libraries (e.g., GDAL, GeoPandas), and map projections
DevOps: Comfortable with Docker, Linux-based deployments, systemd services, and automated backup/cron
Data Security: Implementing encryption, secure protocols (TLS/HTTPS), secure file systems, and user access controls
Testing & Monitoring: Experience with test-driven development, continuous data validation, and pipeline monitoring tools
Strong data analytics, problem-solving, and system optimization skills
Ability to distill and document complex workflows
Able to work independently and proactively in a mission-critical environment
Familiarity with satellite data analysis and GIS is a plus
Experience with processing/backup/restore of large data volumes, long-term archival
Kubernetes or container orchestration experience
Familiarity with object storage solutions

Benefits

We offer great career growth, ESOPs, Gratuity, PF and Health Insurance.",0,"{'error': '429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {\n  quota_metric: ""generativelanguage.googleapis.com/generate_content_free_tier_requests""\n  quota_id: ""GenerateRequestsPerDayPerProjectPerModel-FreeTier""\n  quota_dimensions {\n    key: ""model""\n    value: ""gemini-1.5-flash""\n  }\n  quota_dimensions {\n    key: ""location""\n    value: ""global""\n  }\n  quota_value: 50\n}\n, links {\n  description: ""Learn more about Gemini API quotas""\n  url: ""https://ai.google.dev/gemini-api/docs/rate-limits""\n}\n, retry_delay {\n  seconds: 21\n}\n]'}",Unknown,Unknown,Previous resume,False,2025-07-23 10:40:38.541525,2025-07-25 10:41:07.551790,https://www.linkedin.com/jobs/view/**********,Easy Applied,"{('last name', 'm', 'text', 'm'), ('Phone country code [ ""List of phone country codes"" ]', 'Yes', 'select', 'India (+91)'), ('what is your notice period?', '0', 'text', ''), ('location (city)', 'Bangalore Urban, Karnataka, India', 'text', ''), ('Email address [  ""Select an option"", ""<EMAIL>"", ]', 'Yes', 'select', '<EMAIL>'), ('mobile phone number', '9686355952', 'text', '9686355952'), ('cover letter', 'Cover Letter\n', 'textarea', ''), ('what is your current ctc?', '800000', 'text', ''), ('headline', 'Software Engineer |SDE 1 |  React.js, Node.js, React Native | Full Stack Developer | 2+ years experience | Open for SDE roles', 'text', ''), ('what is your expected ctc?', '1000000', 'text', ''), ('summary', ""I'm a Software Engineer with 2+ years of experience in full-stack web and mobile development.\nSpecialized in React.js, React Native, Node.js, Express.js, TypeScript, and cloud platforms (AWS, Cloudflare).\nBuilt production-ready applications including MyTeal (mental health platform) and TradeStreak (crypto app) with proven performance improvements.\nExperienced in serverless architecture, API development, mobile app development, and modern web technologies.\nWinner of Agoric and Rise In Fast Hack Hackathon (Web3) and finalist at Bank of Baroda Hackathon.\n"", 'textarea', ''), ('first name', 'Sachin', 'text', 'Sachin'), ('how many years of experience do you have?', '3', 'text', '')}",In Development
**********,PCB Design Freelancer,Charcoa,arcoa,arcoa,"About the job
Hi! We're Charcoal.

The world is filled with primitive, dull products that don’t add much value to our lives. A desk is just a surface to hold things up or an article of clothing is just a surface on our skin. We're changing that by leveraging great design and engineering to unlock the potential of these everyday products that have remained stagnant for centuries.

Check out our products here - www.charcoal.inc
Our latest product: https://www.youtube.com/watch?v=ypQFMZZQqXQ

About the Role:
We are seeking a versatile PCB Design Freelancer who can independently drive schematic-to-layout workflows, with a strong foundation in analog component selection, harness design, and power electronics. You will be working on compact, manufacturable, and robust 4-layer PCB designs for consumer or industrial-grade hardware.

This is a technical freelance role requiring attention to detail, design sensibility, and a deep understanding of electronics. The person should be based out of Bangalore.

Key Responsibilities:
Convert schematic diagrams into optimized PCB layouts (including 4-layer boards) with proper stack-up planning.
Select analog and power components based on application needs, availability, cost, and electrical performance.
Design robust power supply circuits (buck/boost converters, LDOs, protection, filtering).
Define harness routing and connector positioning, ensuring electrical safety and mechanical fit.
Generate and manage all deliverables for fabrication and assembly: Gerbers, BOM, Pick-and-Place files, and 3D views.
Apply DFM and DFA principles and communicate with vendors during fabrication if needed.

Required Skills:
Proficiency in PCB design tools like Altium Designer, KiCAD, Eagle, OrCAD, or EasyEDA.
Solid understanding of analog circuit behavior, grounding techniques, and noise mitigation.
Experience with harness and connector design, including power, data, and control cabling.
Proven experience with multi-layer (especially 4-layer) PCB stack-ups for power, analog, and digital domains.
Strong knowledge of DC-DC converters, battery charging circuits, and load protection mechanisms.
Ability to select components based on thermal, electrical, and mechanical parameters.
Familiarity with IPC standards, EMC/EMI best practices, and thermal management.

Nice to Have:
Familiarity with automotive or industrial harness standards (e.g., Molex, JST, TE connectors).
Experience coordinating with PCB manufacturers and harness vendors, especially in India or China.
Understanding of mechanical integration constraints in product enclosures.

Deliverables:
Native PCB design files
Gerber + drill files
Bill of Materials (BOM)
Pick-and-Place files
Assembly notes
3D model (optional)
Harness diagrams or cable schedules (if applicable)",0,"{'error': '429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {\n  quota_metric: ""generativelanguage.googleapis.com/generate_content_free_tier_requests""\n  quota_id: ""GenerateRequestsPerDayPerProjectPerModel-FreeTier""\n  quota_dimensions {\n    key: ""model""\n    value: ""gemini-1.5-flash""\n  }\n  quota_dimensions {\n    key: ""location""\n    value: ""global""\n  }\n  quota_value: 50\n}\n, links {\n  description: ""Learn more about Gemini API quotas""\n  url: ""https://ai.google.dev/gemini-api/docs/rate-limits""\n}\n, retry_delay {\n  seconds: 45\n}\n]'}",Unknown,Unknown,Previous resume,False,2025-07-25 10:20:13.859271,2025-07-25 10:41:18.180762,https://www.linkedin.com/jobs/view/**********,Easy Applied,"{('phone', '9686355952', 'text', '9686355952'), ('Email [  ""Select an option"", ""<EMAIL>"", ]', 'Yes', 'select', '<EMAIL>'), ('Phone country code [ ""List of phone country codes"" ]', 'Yes', 'select', 'India (+91)')}",In Development
4270730426,AI/ML Engineer,Sustainability Economics.a,stainability Economics.a,stainability Economics.a,"About the job
Location: Bengaluru, Karnataka

About the Company:
Sustainability Economics is a global organisation targeting the markets of US, Japan, EU, UK, Canada and Singapore. As a pioneer, our focus is on the Lifecycle Management of E2E Net-Zero transitions while leveraging our automation-first approach. We offer our collaborative services at an international level and counsel our clients on transition solutions and decarbonisation strategies. With our team of professionals who have extensive domain and technical knowledge, we are committed to making long-term efforts to fulfil this vision through our technical innovation, client services, expertise, and capability expansion.

Role Summary:
We are seeking a highly skilled Generative AI and Machine Learning Engineer to join our innovative team. This role blends the frontier of generative AI with robust machine learning and deep learning engineering. You will be responsible for designing, developing, optimizing, and deploying cutting-edge AI systems that power intelligent, scalable, and agent-driven workflows.

Key Tasks and Accountability:
Strong Foundations on the conceptual & implementation aspects of machine & deep learning models.
Experience on using the LLMs, Hugging Face, LangChain, API Design, RAG, Vector Databases, OpenAI APIs, Gemini APIs.
Experience in using optimization algorithms for ML and FastAPI
Keep abreast of the latest advancements in NLP and ML and other deep Learning Models to ensure our solutions remain at the forefront of technology.
Experience with time series models (ARIMA, SARIMA, LSTM, Transformer models, etc.)
Proficiency in machine learning techniques such as Random Forest, XGBoost
Programming Language Experience in Python.
Work closely with domain experts to understand the latest research findings and incorporate them into models.
Collaborate with software developers to create user-friendly interfaces and tools that can be used to visualize and analyze as per the business requirements.

Educational & Experience:
Bachelor's or master's degree in a relevant field, such as engineering, statistics, or Mathematics.
2-3 years of related experience in ML and LLM.
Strong background in AI/ML, with experience developing and implementing models in Python or similar languages
Experience working with large text based datasets and data processing tools
Excellent communication skills, with the ability to explain complex technical concepts to non-technical audiences
Strong problem-solving skills, with the ability to work independently and as part of a team
Passion for environmental sustainability and the ability to work in a fast-paced, dynamic environment

Skills:
Proficient in Large Language Models (LLMs), CrewAI, and other advanced AI models.
Strong expertise in Python, with a solid understanding of object-oriented programming (OOP) principles.
Experience designing scalable system architectures and developing RESTful APIs.
Familiar with web frameworks such as Flask and Django.
Proven ability to build and scale AI/ML products from the ground up.
Background in data analysis, machine learning, deep learning, and natural language processing (NLP).

Personal Attributes:
A team player with good interpersonal skills and problem-solving abilities.
A self-motivator and a collaborative team player.
Shows strong leadership by guiding and motivating the team
Excellent time management skills and the ability to assume leadership roles when necessary.
Agile, action oriented and a quick learner.
Out of box thinker.

Benefits at Sustainability Economics:
A flat hierarchy.
ESOPs.
Group Medical Coverage.
30 days of Leave.
A gender-neutral parental, marriage, and compassionate leave policy.

And above all, if you have a passion to work, join us in our pursuit for sustainability",2,"{'error': '429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {\n  quota_metric: ""generativelanguage.googleapis.com/generate_content_free_tier_requests""\n  quota_id: ""GenerateRequestsPerDayPerProjectPerModel-FreeTier""\n  quota_dimensions {\n    key: ""model""\n    value: ""gemini-1.5-flash""\n  }\n  quota_dimensions {\n    key: ""location""\n    value: ""global""\n  }\n  quota_value: 50\n}\n, links {\n  description: ""Learn more about Gemini API quotas""\n  url: ""https://ai.google.dev/gemini-api/docs/rate-limits""\n}\n, retry_delay {\n  seconds: 41\n}\n]'}",Unknown,Unknown,Previous resume,False,2025-07-23 10:41:17.795856,2025-07-25 10:41:22.998498,https://www.linkedin.com/jobs/view/4270730426,Easy Applied,"{('Phone country code [ ""List of phone country codes"" ]', 'Yes', 'select', 'India (+91)'), ('Email address [  ""Select an option"", ""<EMAIL>"", ]', 'Yes', 'select', '<EMAIL>'), ('mobile phone number', '9686355952', 'text', '9686355952'), ('Are you comfortable working in an onsite setting? [  ""Yes""<Yes>, ""No""<No>, ]', '""Yes""<Yes>', 'radio', '""Yes""<Yes>'), ('Have you completed the following level of education: Bachelor\'s Degree? [  ""Yes""<Yes>, ""No""<No>, ]', '""Yes""<Yes>', 'radio', '""Yes""<Yes>'), ('Will you be able to join us immediately? [  ""Select an option"", ""Yes"", ""No"", ]', 'Yes', 'select', 'Select an option')}",In Development
4275396068,Python Automation Test Engineer,EPAM System,AM System,AM System,"About the job
We are in search of a dedicated Python Automation Test Engineer to join our team, spearheading the automated testing process. This is an exceptional opportunity for a testing professional with a strong background in Python automation and experience in software and system testing.

Responsibilities


Provide Python automation testing efforts within the team
Develop and execute test cases and scripts to ensure the quality and reliability of our software products
Collaborate with cross-functional teams to understand testing requirements and provide testing support
Conduct thorough testing of SOAP and REST services to validate system functionality
Create and maintain comprehensive test documentation, including checklists and test cases
Utilize strong analytical skills to troubleshoot and resolve testing issues
Design and implement formal test cases based on software requirements
Contribute to the continuous improvement of testing processes and methodologies
Participate in Scrum ceremonies and adhere to Agile principles in testing activities


Requirements


3-5 years of strong experience in Selenium Automation using Python
Experience in software testing or development with good understanding of testing, coding and debugging procedures
Strong experience in BDD Framework like Pytest
Experience working with SOAP and REST service and understanding of SOA architecture
Knowledge of various software testing techniques and methods, as well as experience applying them
Experience maintaining test documentation (check-lists, test cases, etc.)
Experience with Oracle database and strong SQL knowledge
Strong analytical skills and attention to details
Strong troubleshooting skills
Ability to design formal test cases for testing, based on requirements
Understanding of the Scrum main terms and principles
B2+ English level proficiency",3,"{'error': '429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {\n  quota_metric: ""generativelanguage.googleapis.com/generate_content_free_tier_requests""\n  quota_id: ""GenerateRequestsPerDayPerProjectPerModel-FreeTier""\n  quota_dimensions {\n    key: ""model""\n    value: ""gemini-1.5-flash""\n  }\n  quota_dimensions {\n    key: ""location""\n    value: ""global""\n  }\n  quota_value: 50\n}\n, links {\n  description: ""Learn more about Gemini API quotas""\n  url: ""https://ai.google.dev/gemini-api/docs/rate-limits""\n}\n, retry_delay {\n  seconds: 22\n}\n]'}",Unknown,Unknown,Previous resume,False,2025-07-24 20:41:37.751136,2025-07-25 10:41:41.935337,https://www.linkedin.com/jobs/view/4275396068,Easy Applied,"{('last name', 'm', 'text', 'm'), ('Phone country code [ ""List of phone country codes"" ]', 'Yes', 'select', 'India (+91)'), ('Email address [  ""Select an option"", ""<EMAIL>"", ]', 'Yes', 'select', '<EMAIL>'), ('mobile phone number', '9686355952', 'text', '9686355952'), ('first name', 'Sachin', 'text', 'Sachin')}",In Development
4271815324,FPGA RTL Design,TAGDES Technologies Pvt Lt,GDES Technologies Pvt Lt,GDES Technologies Pvt Lt,"About the job
We, TAGDES Technologies, are in the path of growing our FPGA Logic Design Team.

We are looking out for young minds who are eager to work in fast-paced start-up environment. We offer a great opportunity to work and learn on Defence Electronics, IOT and Robotics.

Engineering graduates, BE/B.Tech or Diploma in E&C/E&I , with experience in VHDL/ Verilog based RTL Design, having 2+ yrs experience can reachout to us:

<EMAIL>.

Required Skills:
HDL coding in VHDL/Verilog.
Programming in Python/Perl.
Expertise on AMD/Intel FPGAs.
Experience with RFSOC/ Zynq Ultrascale or Gigasample RF ADC and DAC is must.
Protocols: Ethernet(UDP), PCIe, SPI, I2C, DDR4, JESD.

#FPGA #AMD #FPGAJOBs #ITJOBS #HIRING #RECRUITING #FRESHERS #CDAC #NILIET",0,"{'error': '429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {\n  quota_metric: ""generativelanguage.googleapis.com/generate_content_free_tier_requests""\n  quota_id: ""GenerateRequestsPerDayPerProjectPerModel-FreeTier""\n  quota_dimensions {\n    key: ""model""\n    value: ""gemini-1.5-flash""\n  }\n  quota_dimensions {\n    key: ""location""\n    value: ""global""\n  }\n  quota_value: 50\n}\n, links {\n  description: ""Learn more about Gemini API quotas""\n  url: ""https://ai.google.dev/gemini-api/docs/rate-limits""\n}\n, retry_delay {\n  seconds: 3\n}\n]'}",Unknown,Unknown,Previous resume,False,2025-07-24 20:41:56.015935,2025-07-25 10:42:00.194343,https://www.linkedin.com/jobs/view/4271815324,Easy Applied,"{('phone', '9686355952', 'text', '9686355952'), ('Email [  ""Select an option"", ""<EMAIL>"", ]', 'Yes', 'select', '<EMAIL>'), ('Phone country code [ ""List of phone country codes"" ]', 'Yes', 'select', 'India (+91)')}",In Development
4271596608,"Project-Based Technical Trainer (AI/ML, Full Stack, Embedded systems, Data Analytics, Cyber Security)",Take It Smart (OPC) Pvt Lt,ke It Smart,OPC,"About the job
We Are looking for dedicated and experienced IT Trainers who can strike the right balance between conceptual teaching and practical, project-based training.

✅ Were Hiring Trainers in the Following Domains:
AI / ML / Data Science
Full Stack Web Development (Java / Python)
Cyber Security
Data Analytics
Embedded Systems

Responsibilities:

Delivering clear and engaging conceptual/theoretical explanation of topics
Conducting practical sessions with real-time use cases and projects
Follow up with hands-on training and real-time project guidance
Mentoring students on major projects
Preparing learners for technical interviews, coding tests, and hackathons
Updating curriculum regularly based on the latest technologies and industry needs
Taking ownership of student success and placement-oriented learning outcomes

✅ Multi-domain trainers Highly preferred.

Key Requirements:

Strong command of technical fundamentals
Prior experience delivering practical training and project-based learning
Excellent communication & presentation skills
Ability to manage live classes, offline batches, and hybrid groups
Student-focused approach with a passion for mentoring",0,"{'error': '429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {\n  quota_metric: ""generativelanguage.googleapis.com/generate_content_free_tier_requests""\n  quota_id: ""GenerateRequestsPerDayPerProjectPerModel-FreeTier""\n  quota_dimensions {\n    key: ""model""\n    value: ""gemini-1.5-flash""\n  }\n  quota_dimensions {\n    key: ""location""\n    value: ""global""\n  }\n  quota_value: 50\n}\n, links {\n  description: ""Learn more about Gemini API quotas""\n  url: ""https://ai.google.dev/gemini-api/docs/rate-limits""\n}\n, retry_delay {\n  seconds: 46\n}\n]'}",Unknown,Unknown,Previous resume,False,2025-07-24 18:42:13.501627,2025-07-25 10:42:19.698055,https://www.linkedin.com/jobs/view/4271596608,Easy Applied,"{('Email address [  ""Select an option"", ""<EMAIL>"", ]', 'Yes', 'select', '<EMAIL>'), ('how many years of professional training and coaching experience do you currently have?', '3', 'text', ''), ('Are you comfortable commuting to this job\'s location? [  ""Yes""<Yes>, ""No""<No>, ]', 'Yes', 'radio', None), ('mobile phone number', '9686355952', 'text', '9686355952'), ('how many years of training experience do you currently have?', '3', 'text', ''), ('Are you comfortable working in a hybrid setting? [  ""Yes""<Yes>, ""No""<No>, ]', '""Yes""<Yes>', 'radio', '""Yes""<Yes>'), ('Are you comfortable working in an onsite setting? [  ""Yes""<Yes>, ""No""<No>, ]', '""Yes""<Yes>', 'radio', '""Yes""<Yes>'), ('We must fill this position urgently. Can you start immediately? [  ""Yes""<Yes>, ""No""<No>, ]', '""Yes""<Yes>', 'radio', '""Yes""<Yes>'), ('Phone country code [ ""List of phone country codes"" ]', 'Yes', 'select', 'India (+91)')}",In Development
4271810131,MEA Trainers Sourcing / Hiring Trainers Bangalore (Work from Office),The Knowledge Academ,e Knowledge Academ,e Knowledge Academ,"About the job
Job description

We are looking for a reliable operations associate to support the operations manager with the management of the day-to-day business. The operations associate's responsibilities include sourcing of trainers. A successful operations associate should be highly organized and have a versatile skill set to support a variety of different business functions with a diverse range of tasks.

RESPONSIBILITIES

• Trainer acquisition for Cloud Technologies such as (AWS, Azure, DevOps, GCP), BI Technologies such as (Power BI, Tableau, Qlik sense, QlikView), Full stack Technologies such as (Angular JS, React JS, Node JS), Python
• Organizing and Managing IT Technical Trainings outside India i.e International Market
• Scheduling calls with SMEs for various IT training program
• Search freelance trainers on social media platforms such as LinkedIn
• Handle Corporate Trainings
• Manage administrative tasks, such as scheduling, training requirements, training attendance and communication.
• Maintain database of SMEs selected with skillsets on regular basis in Google Sheet and Excel and keep pipeline of Trainers ready for future training programs.

REQUIREMENTS

• 1yr of experience in Sourcing Trainers
• Strong analytical and organizational skills.
• Excellent communication and listening skills.
• Ability to work under pressure.
• Strong computer proficiency.
• Must be detail and solutions-oriented",0,"{'error': '429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {\n  quota_metric: ""generativelanguage.googleapis.com/generate_content_free_tier_requests""\n  quota_id: ""GenerateRequestsPerDayPerProjectPerModel-FreeTier""\n  quota_dimensions {\n    key: ""model""\n    value: ""gemini-1.5-flash""\n  }\n  quota_dimensions {\n    key: ""location""\n    value: ""global""\n  }\n  quota_value: 50\n}\n, links {\n  description: ""Learn more about Gemini API quotas""\n  url: ""https://ai.google.dev/gemini-api/docs/rate-limits""\n}\n, retry_delay {\n  seconds: 33\n}\n]'}",Ojasvi Mahajan ,https://www.linkedin.com/in/ojasvim,Previous resume,False,2025-07-24 18:42:26.215712,2025-07-25 10:42:32.683107,https://www.linkedin.com/jobs/view/4271810131,Easy Applied,"{('Email address [  ""Select an option"", ""<EMAIL>"", ]', 'Yes', 'select', '<EMAIL>'), ('The job is for Bangalore Location work from office. Timings will be from 10am to 7pm Sunday to Thursday. If you are comfortable for the Timings, then only Apply. [  ""Select an option"", ""Yes"", ""No"", ]', 'No', 'select', 'Select an option'), ('Our budget is of 35,000 so apply accordingly. [  ""Select an option"", ""Yes"", ""No"", ]', 'Yes', 'select', 'Select an option'), ('mobile phone number', '9686355952', 'text', '9686355952'), ('how many years of experience in b2b operations / trainer sme as you need to find trainers', '3', 'text', ''), ('What is your level of proficiency in English? [  ""Select an option"", ""None"", ""Conversational"", ""Professional"", ""Native or bilingual"", ]', 'Yes', 'select', 'Professional'), ('We must fill this position urgently. Can you start immediately? [  ""Yes""<Yes>, ""No""<No>, ]', '""Yes""<Yes>', 'radio', '""Yes""<Yes>'), ('Phone country code [ ""List of phone country codes"" ]', 'Yes', 'select', 'India (+91)')}",In Development
