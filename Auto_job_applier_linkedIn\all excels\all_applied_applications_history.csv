Job ID,Title,Company,Work Location,Work Style,About Job,Experience required,Skills required,HR Name,HR Link,Resume,Re-posted,Date Posted,Date Applied,Job Link,External Job link,Questions Found,Connect Request
4272061237,Application Developer - Cloud Fullstack,KGiS,iS,iS,"About the job
What You’ll Do:

✔ Analyze software requirements and design solutions aligned with business needs
✔ Develop, test, and integrate software components for energy automation products
✔ Conduct code, design, and documentation reviews
✔ Create and maintain technical documentation (requirements, design, test cases)
✔ Ensure high-quality deliverables within committed timelines
✔ Collaborate with stakeholders on requirements, design, and continuous delivery

What You Bring:

✅ Strong expertise in Java, Spring Framework, PostgreSQL, and AWS
✅ Experience in frontend development (Angular, Bootstrap) – a big plus!
✅ Solid understanding of object-oriented programming & software design
✅ Exposure to multi-country software development & Agile/DevOps practices
✅ Strong analytical, problem-solving, and communication skills
✅ Ability to learn new technologies quickly and work with cross-functional teams",0,"{'tech_stack': ['Java', 'Spring Framework', 'PostgreSQL', 'AWS', 'Angular', 'Bootstrap'], 'technical_skills': ['Software design', 'object-oriented programming', 'Agile/DevOps practices', 'System Architecture'], 'other_skills': ['Analytical skills', 'Problem-solving skills', 'Communication skills', 'Teamwork'], 'required_skills': ['Java', 'Spring Framework', 'PostgreSQL', 'AWS', 'Solid understanding of object-oriented programming & software design', 'Exposure to multi-country software development & Agile/DevOps practices', 'Strong analytical, problem-solving, and communication skills', 'Ability to learn new technologies quickly and work with cross-functional teams'], 'nice_to_have': ['Experience in frontend development (Angular, Bootstrap)']}",Karankumar R ,https://www.linkedin.com/in/karankumar-r-319721226,Previous resume,False,2025-07-25 17:20:27.968742,2025-07-25 17:34:34.178898,https://www.linkedin.com/jobs/view/4272061237,Easy Applied,"{('how many years of work experience do you have with amazon web services (aws)?', '2', 'text', '2'), ('how many years of work experience do you have with java?', '2', 'text', '2'), ('Email address [  ""Select an option"", ""<EMAIL>"", ]', 'Yes', 'select', '<EMAIL>'), ('mobile phone number', '9686355952', 'text', '9686355952'), ('how many years of work experience do you have with bootstrap (framework)?', '3', 'text', ''), ('Phone country code [ ""List of phone country codes"" ]', 'Yes', 'select', 'India (+91)')}",In Development
4270181276,Manual Testing,Samsung Indi,msung Indi,msung Indi,"About the job
Job Description Summary
Ensure the software release quality through activities including monitoring and approving software process execution.
Improving software process based on analysis of failure and customer needs.

Job Description
Hands-on experience in mobile application & Web testing (Black box & API level)
Good hand on experience in Manual Testing
Good knowledge in Mobile Testing concepts and testing experience will be added advantage
Good knowledge in Appium/UI Automator, SoapUI/Postman and Selenium tools
Good experience in Python/Java scripting language
Experience in Automation tool development (added advantage)

Additional Job Description
Learns to use professional concepts. Applies company policies and procedures to resolve routine issues
Works on problems of limited scope. Follows standard practices and procedures
Normally receives detailed instructions on all work
Typical entry point for university graduates",0,"{'error': 'Unable to parse the response as JSON', 'data': '\n{\n  ""tech_stack"": [""Appium"", ""UI Automator"", ""SoapUI"", ""Postman"", ""Selenium"", ""Python"", ""Java""],\n  ""technical_skills"": [""Mobile application testing"", ""Web testing"", ""API testing"", ""Manual Testing"", ""Automation tool development""],\n  ""other_skills"": [],\n  ""required_skills"": [""Hands-on experience in mobile application & Web testing (Black box & API level)"", ""Good hand on experience in Manual Testing"", ""Good knowledge in Mobile Testing concepts""],\n  ""nice_to_have"": [""Good knowledge in Appium/UI Automator, SoapUI/Postman and Selenium tools"", ""Good experience in Python/Java scripting language"", ""Experience in Automation tool development""]\n}\n```\n'}",Nazneen Perween (Naz) ,https://www.linkedin.com/in/nazneenperween,Previous resume,False,2025-07-25 17:19:40.758360,2025-07-25 17:34:47.373245,https://www.linkedin.com/jobs/view/4270181276,Easy Applied,"{('Email address [  ""Select an option"", ""<EMAIL>"", ]', 'Yes', 'select', '<EMAIL>'), ('mobile phone number', '9686355952', 'text', '9686355952'), ('Phone country code [ ""List of phone country codes"" ]', 'Yes', 'select', 'India (+91)')}",In Development
4272010647,QA Automation Developer,GRI,I,I,"About the job
Job Title: QA Automation Developer
Location: Bangalore, India
Experience Level: 3–5 Years
Employment Type: Full-Time

Job Description

We are hiring a QA Automation Developer on behalf of our client, a fast-growing company working on innovative cloud and SaaS technologies. This role requires a proactive individual with a strong foundation in software quality assurance and automation testing. The ideal candidate will play a key role in improving the quality, performance, and scalability of enterprise-level solutions.

Responsibilities

Review requirements, specifications, and technical design documents to provide meaningful and timely feedback.
Develop detailed, comprehensive, and well-structured test plans and test cases.
Estimate, prioritise, plan, and coordinate testing activities.
Design, develop, and execute automation scripts using tools such as Selenium and JMeter.
Identify, record, document, and thoroughly track bugs.
Perform thorough regression testing after bug fixes.
Define and implement testing processes for both new and existing products.
Track QA metrics, such as defect density and open defect counts.
Stay updated on new testing tools and strategies to improve testing efficiency and coverage.

Qualifications

Bachelor’s or Master’s degree in Computer Science or a related technical field.
3–5 years of experience in software development and QA automation.
Hands-on experience with automated testing tools like Selenium and JMeter.
Strong knowledge of QA methodologies, tools, and best practices.
Experience in writing clear and comprehensive test plans and test cases.
Skilled in both white-box and black-box testing.
Solid understanding of SQL and scripting.
Experience working in an Agile/Scrum development environment.
Experience with performance and/or security testing is a plus.
A strong foundation in algorithms is preferred.
Excellent communication and written skills.
Experience with cloud platforms, such as AWS, Google Cloud, or Azure, is a plus.

If you're passionate about quality assurance and eager to work on meaningful cloud-based projects, we’d love to hear from you. This is a great opportunity to join a dynamic and collaborative team focused on building high-performance solutions.",5,"{'error': 'Unable to parse the response as JSON', 'data': '\n{\n  ""tech_stack"": [""Selenium"", ""JMeter"", ""SQL""],\n  ""technical_skills"": [""QA automation"", ""Software quality assurance"", ""Automated testing"", ""Performance testing"", ""Security testing"", ""System Architecture"", ""Agile/Scrum"", ""Algorithms""],\n  ""other_skills"": [""Communication skills"", ""Teamwork""],\n  ""required_skills"": [""QA automation"", ""Selenium"", ""JMeter"", ""Software quality assurance"", ""Automated testing"", ""SQL"", ""Agile/Scrum"", ""Communication skills"", ""Writing clear and comprehensive test plans and test cases"", ""White-box and black-box testing""],\n  ""nice_to_have"": [""Performance testing"", ""Security testing"", ""Algorithms"", ""Cloud platforms (AWS, Google Cloud, Azure)""]\n}\n```\n'}",Suga N.,https://www.linkedin.com/in/suga-n-763ba0160,schin_m resume.pdf,False,2025-07-25 12:40:45.015424,2025-07-25 17:40:54.029797,https://www.linkedin.com/jobs/view/4272010647,Easy Applied,"{('Phone country code [ ""List of phone country codes"" ]', 'Yes', 'select', 'India (+91)'), ('Are you comfortable commuting to this job\'s location? [  ""Yes""<Yes>, ""No""<No>, ]', 'Yes', 'radio', None), ('Are you comfortable working in an onsite setting? [  ""Yes""<Yes>, ""No""<No>, ]', '""Yes""<Yes>', 'radio', '""Yes""<Yes>'), ('We must fill this position urgently. Can you start immediately? [  ""Yes""<Yes>, ""No""<No>, ]', '""Yes""<Yes>', 'radio', '""Yes""<Yes>'), ('Email address [  ""Select an option"", ""<EMAIL>"", ]', 'Yes', 'select', '<EMAIL>'), ('how many years of work experience do you have with azure sql?', '1', 'text', ''), ('mobile phone number', '9686355952', 'text', '9686355952'), ('how many years of work experience do you have with selenium?', '2', 'text', '2')}",In Development
